# 🚀 دليل النشر والتشغيل - <PERSON><PERSON><PERSON> <PERSON><PERSON>

دليل شامل خطوة بخطوة لنشر وتشغيل نظام Aqar Bot على Google Apps Script.

## 📋 قائمة المراجعة السريعة

- [ ] إعداد Google Apps Script Project
- [ ] رفع ملفات الكود
- [ ] تكوين Script Properties
- [ ] ربط Google Forms و Sheets
- [ ] إعداد Triggers
- [ ] تكوين Google Chat Bot
- [ ] اختبار النظام
- [ ] تفعيل المعالجة التلقائية

## 🔧 الخطوة 1: إعداد Google Apps Script

### 1.1 إنشاء مشروع جديد
1. اذهب إلى [Google Apps Script](https://script.google.com)
2. انقر على "مشروع جديد"
3. غير اسم المشروع إلى "A<PERSON><PERSON>"
4. احفظ المشروع

### 1.2 إعداد المكتبات المطلوبة
```javascript
// لا توجد مكتبات خارجية مطلوبة
// جميع الوظائف مبنية باستخدام Google Apps Script APIs
```

## 📁 الخطوة 2: رفع ملفات الكود

### 2.1 إنشاء الملفات
أنشئ الملفات التالية في Google Apps Script:

#### أ) main.js
```javascript
// انسخ محتوى ملف main.js كاملاً
```

#### ب) utils.js
```javascript
// انسخ محتوى ملف utils.js كاملاً
```

#### ج) validation.js
```javascript
// انسخ محتوى ملف validation.js كاملاً
```

#### د) notifications.js
```javascript
// انسخ محتوى ملف notifications.js كاملاً
```

#### هـ) setup.js
```javascript
// انسخ محتوى ملف setup_updated.js كاملاً
```

### 2.2 ترتيب الملفات
تأكد من ترتيب الملفات كالتالي:
1. setup.js (يجب أن يكون الأول)
2. utils.js
3. validation.js
4. notifications.js
5. main.js (يجب أن يكون الأخير)

## ⚙️ الخطوة 3: تكوين Script Properties

### 3.1 تشغيل دالة الإعداد
1. اختر دالة `setupProject` من ملف setup.js
2. انقر على "تشغيل"
3. امنح الصلاحيات المطلوبة
4. تأكد من ظهور رسالة "Setup completed successfully"

### 3.2 التحقق من Properties
```javascript
// شغل هذه الدالة للتحقق
function checkProperties() {
  const props = PropertiesService.getScriptProperties().getProperties();
  console.log('Properties count:', Object.keys(props).length);
  return props;
}
```

## 🔗 الخطوة 4: ربط Google Services

### 4.1 ربط Google Form
1. افتح [Google Form](https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/edit)
2. اذهب إلى "الردود" → "إنشاء جدول بيانات"
3. اختر الجدول الموجود: `Elsweedy real estate sheet`
4. تأكد من أن الورقة تسمى `Aqar_bot`

### 4.2 التحقق من Google Sheets
1. افتح [Google Sheet](https://docs.google.com/spreadsheets/d/1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c/edit)
2. تأكد من وجود ورقة `Aqar_bot`
3. تحقق من أعمدة البيانات

### 4.3 إعداد ورقة الإشعارات
1. افتح [Notification Log](https://docs.google.com/spreadsheets/d/1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM/edit)
2. تأكد من وجود الأعمدة التالية:
   - Timestamp
   - Unit Code
   - Message ID
   - Status
   - Details

## ⏰ الخطوة 5: إعداد Triggers

### 5.1 Form Submit Trigger
1. اذهب إلى "المشغلات" في Google Apps Script
2. انقر على "إضافة مشغل"
3. اختر:
   - **الدالة**: `onFormSubmit`
   - **مصدر الحدث**: من جدول البيانات
   - **نوع الحدث**: عند إرسال النموذج

### 5.2 AI Processor Trigger
1. أضف مشغل جديد:
   - **الدالة**: `aiProcessor`
   - **مصدر الحدث**: مؤقت
   - **نوع المؤقت**: كل 5 دقائق

### 5.3 Fallback Processor Trigger
1. أضف مشغل جديد:
   - **الدالة**: `fallbackProcessor`
   - **مصدر الحدث**: مؤقت
   - **نوع المؤقت**: كل 30 دقيقة

## 💬 الخطوة 6: إعداد Google Chat Bot

### 6.1 إنشاء Chat App
1. اذهب إلى [Google Chat API Console](https://console.cloud.google.com/apis/api/chat.googleapis.com)
2. فعل Google Chat API
3. أنشئ Chat App جديد
4. احصل على Space ID و App ID

### 6.2 تحديث إعدادات Chat
```javascript
// شغل هذه الدالة لتحديث إعدادات Chat
function updateChatSettings() {
  const properties = PropertiesService.getScriptProperties();
  properties.setProperties({
    'CHAT_SPACE_ID': 'YOUR_SPACE_ID_HERE',
    'CHAT_APP_ID': 'YOUR_APP_ID_HERE'
  });
}
```

## 🧪 الخطوة 7: اختبار النظام

### 7.1 اختبار Form Submission
1. افتح [Google Form](https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/viewform)
2. املأ النموذج ببيانات تجريبية
3. أرسل النموذج
4. تحقق من:
   - إضافة صف جديد في Google Sheet
   - توليد كود الوحدة
   - إرسال إشعار (إذا تم تكوين Chat)

### 7.2 اختبار AI Processing
```javascript
// شغل هذه الدالة لاختبار المعالجة
function testAIProcessing() {
  try {
    aiProcessor();
    console.log('AI Processing test completed');
  } catch (error) {
    console.error('AI Processing test failed:', error);
  }
}
```

### 7.3 اختبار Notifications
```javascript
// شغل هذه الدالة لاختبار الإشعارات
function testNotifications() {
  try {
    sendOrUpdateNotification('TEST-001', 'تجربة', {
      message: 'هذا اختبار للإشعارات'
    }, true);
    console.log('Notification test completed');
  } catch (error) {
    console.error('Notification test failed:', error);
  }
}
```

## 🔍 الخطوة 8: مراقبة الأداء

### 8.1 مراجعة السجلات
1. اذهب إلى "التنفيذ" في Google Apps Script
2. راجع سجلات التنفيذ
3. تحقق من عدم وجود أخطاء

### 8.2 مراقبة استهلاك API
- تحقق من استهلاك OpenAI API
- راقب استهلاك Google APIs
- تأكد من عدم تجاوز الحدود

## ⚠️ استكشاف الأخطاء الشائعة

### خطأ: "Permission denied"
**الحل**: تأكد من منح جميع الصلاحيات المطلوبة

### خطأ: "API key invalid"
**الحل**: تحقق من صحة مفاتيح API في Script Properties

### خطأ: "Sheet not found"
**الحل**: تأكد من صحة IDs في ملف الإعداد

### خطأ: "Trigger not working"
**الحل**: احذف وأعد إنشاء المشغلات

## 📊 الخطوة 9: التحقق النهائي

### قائمة مراجعة التشغيل
- [ ] Form يرسل البيانات إلى Sheet
- [ ] يتم توليد كود الوحدة تلقائياً
- [ ] AI Processor يعمل كل 5 دقائق
- [ ] Fallback Processor يعمل كل 30 دقيقة
- [ ] الإشعارات تصل إلى Google Chat
- [ ] البيانات تُحفظ في Notification Log
- [ ] لا توجد أخطاء في السجلات

## 🎯 الخطوة 10: التشغيل الإنتاجي

### تفعيل النظام
1. تأكد من جميع الاختبارات
2. فعل جميع المشغلات
3. أعلن عن جاهزية النظام
4. ابدأ مراقبة الأداء

### صيانة دورية
- راجع السجلات أسبوعياً
- نظف البيانات القديمة شهرياً
- حدث مفاتيح API عند الحاجة
- راجع أداء النظام ربع سنوياً

---

**ملاحظة**: احتفظ بنسخة احتياطية من جميع الإعدادات والمفاتيح في مكان آمن.
