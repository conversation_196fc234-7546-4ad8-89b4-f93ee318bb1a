# Aqar Bot Environment Variables
# Copy this file to .env and fill in your actual values
# NEVER commit .env file to version control

# ===== Google Services Configuration =====
GOOGLE_FORM_ID=1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw
GOOGLE_FORM_URL=https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/viewform
SHEET_ID=1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c
SHEET_NAME=Aqar_bot
NOTIFICATIONS_LOG_SHEET_ID=1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM
PROMPT_DOC_ID=14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk

# ===== Project Names =====
FORM_NAME=ضع بيان عقارك هنا
FOLDER_NAME=Elsweedy real estate data
MAIN_SHEET_NAME=Elsweedy real estate sheet
NOTIFICATION_SHEET_NAME=notification log
PROMPT_DOC_NAME=Flexible Real Estate Analysis Prompt

# ===== Google Chat Configuration =====
CHAT_SPACE_ID=
CHAT_APP_ID=
CHAT_WEBHOOK_URL=

# ===== AI API Keys =====
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

MISTRAL_API_KEY=your_mistral_api_key_here
MISTRAL_MODEL=mistral-large

GROQ_API_KEY=your_groq_api_key_here
GROQ_MODEL=llama3-70b-8192

HUGGINGFACE_API_KEY=your_huggingface_api_key_here
HUGGINGFACE_MODEL=meta-llama/Llama-2-70b-chat-hf

# Multiple Gemini API Keys for redundancy
GEMINI_API_KEY_1=your_gemini_api_key_1_here
GEMINI_API_KEY_2=your_gemini_api_key_2_here
GEMINI_API_KEY_3=your_gemini_api_key_3_here
GEMINI_MODEL=gemini-pro

# ===== Service Account Configuration =====
# Paste your entire service account JSON here as a single line
SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your_project_id",...}

# ===== Field Names (Arabic) =====
UNIT_CODE_COL=كود الوحدة
STATUS_COL=Status
SUCCESS_STATES_COL=حالات النجاح
PROPERTY_TYPE_QUESTION=نوع البيان اللي بتسجله     ⁉️صله
SINGLE_VALUE_FIELD=🪀  بيان  عقار  غير مفصل
DETAILED_TYPE=بيان  مفصل  💘
MULTI_VALUE_FIELD=🔥  بيانات  متعددة  🔥

# ===== Default Values =====
DEFAULT_TEXT_VALUE=غير محدد
DEFAULT_NUMBER_VALUE=0
DEFAULT_PHONE_VALUE=***********
