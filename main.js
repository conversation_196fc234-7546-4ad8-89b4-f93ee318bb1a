/**
 * Main entry point for the Real Estate Data Processing System
 * Handles form submissions and coordinates the workflow
 */

// ===== CONFIGURATION =====
const CONFIG = {
  SHEET_NAME: 'Aqar_bot',
  UNIT_CODE_COL: 'كود الوحدة',
  STATUS_COL: 'Status',
  SUCCESS_STATES_COL: 'حالات النجاح',
  PROPERTY_TYPE_QUESTION: 'نوع البيان اللي بتسجله     ❓❔',
  MULTI_VALUE_FIELD: '🪀 بيانات  متعددة  غير مفصله',
  SINGLE_VALUE_FIELD: '🪀  بيان  عقار  غير مفصل',
  DETAILED_TYPE: 'بيان  مفصل  💘'
};

// ===== MAIN FUNCTIONS =====

/**
 * Triggered when a new form submission is received
 * @param {Object} e - Form submission event object
 */
function onFormSubmit(e) {
  try {
    console.log('Processing new form submission...');
    
    // Get sheet and response data
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    const lastRow = sheet.getLastRow();
    const headers = getHeadersMap(sheet);
    
    // Set default values for empty fields
    setDefaultValues(sheet, lastRow, headers);
    
    // Generate and set unit code
    const unitCode = generateUnitCode(sheet, headers[CONFIG.UNIT_CODE_COL]);
    sheet.getRange(lastRow, headers[CONFIG.UNIT_CODE_COL]).setValue(unitCode);
    
    // Update success states
    updatePropertyStatus(sheet, lastRow, headers[CONFIG.SUCCESS_STATES_COL], 'تخزين');
    
    // Send initial notification
    sendOrUpdateNotification(unitCode, 'تخزين', {
      row: lastRow,
      timestamp: new Date()
    }, true);
    
    // Get property type for processing
    const propertyType = sheet.getRange(lastRow, headers[CONFIG.PROPERTY_TYPE_QUESTION]).getValue();
    
    // Handle based on property type
    if (propertyType === CONFIG.MULTI_VALUE_FIELD) {
      handleMultipleProperties(sheet, lastRow, headers);
    } else if (propertyType === CONFIG.SINGLE_VALUE_FIELD) {
      // Mark for AI processing
      markForAIProcessing(sheet, lastRow, headers);
    } else if (propertyType === CONFIG.DETAILED_TYPE) {
      // Already processed, just check for damaged
      checkAndMarkDamaged(sheet, lastRow, headers, unitCode, headers[CONFIG.STATUS_COL]);
    }
    
    console.log('Form submission processed successfully');
    
  } catch (error) {
    console.error('Error in onFormSubmit:', error);
    handleError(error, 'Form Submission');
  }
}

/**
 * AI Processor - Runs on schedule to process properties with AI
 */
function aiProcessor() {
  try {
    console.log('Starting AI processor...');
    
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    const headers = getHeadersMap(sheet);
    const dataRange = sheet.getDataRange();
    const data = dataRange.getValues();
    
    // Find rows that need AI processing
    for (let i = 1; i < data.length; i++) {
      const row = i + 1;
      const successStates = data[i][headers[CONFIG.SUCCESS_STATES_COL] - 1] || '';
      const status = data[i][headers[CONFIG.STATUS_COL] - 1] || '';
      
      // Skip if already processed or failed completely
      if (successStates.includes('تفكيك') || status.includes('عقار فاشل')) {
        continue;
      }
      
      const unitCode = data[i][headers[CONFIG.UNIT_CODE_COL] - 1];
      const statement = data[i][headers[CONFIG.SINGLE_VALUE_FIELD] - 1];
      
      if (!statement || statement === 'غير محدد') {
        continue;
      }
      
      console.log(`Processing unit ${unitCode} with AI...`);
      
      // Process with AI (implementation in utils.js)
      const result = processWithAI(unitCode, statement);
      
      if (result.success) {
        // Update fields with AI results
        updatePropertyFromAIResult(sheet, row, headers, result.data);
        
        // Update success states
        updatePropertyStatus(sheet, row, headers[CONFIG.SUCCESS_STATES_COL], 'تفكيك');
        
        // Send success notification
        sendOrUpdateNotification(unitCode, 'تفكيك', {
          aiResult: result.data,
          row: row
        }, false);
        
      } else {
        // Mark as failed
        updatePropertyStatus(sheet, row, headers[CONFIG.STATUS_COL], 'عقار فاشل');
        
        // Send failure notification
        sendOrUpdateNotification(unitCode, 'فشل', {
          error: result.error,
          row: row,
          stage: 'AI Processing'
        }, false);
      }
    }
    
    console.log('AI processor completed');
    
  } catch (error) {
    console.error('Error in aiProcessor:', error);
    handleError(error, 'AI Processor');
  }
}

/**
 * Fallback Processor - Processes failed properties locally
 */
function fallbackProcessor() {
  try {
    console.log('Starting fallback processor...');
    
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
    const headers = getHeadersMap(sheet);
    const dataRange = sheet.getDataRange();
    const data = dataRange.getValues();
    
    // Find failed rows
    for (let i = 1; i < data.length; i++) {
      const row = i + 1;
      const status = data[i][headers[CONFIG.STATUS_COL] - 1] || '';
      const successStates = data[i][headers[CONFIG.SUCCESS_STATES_COL] - 1] || '';
      
      // Process only failed properties that haven't been analyzed
      if (status.includes('عقار فاشل') && !successStates.includes('تفكيك')) {
        const unitCode = data[i][headers[CONFIG.UNIT_CODE_COL] - 1];
        const statement = data[i][headers[CONFIG.SINGLE_VALUE_FIELD] - 1];
        
        console.log(`Processing unit ${unitCode} with fallback...`);
        
        // Get prompt from Google Docs
        const promptDoc = getPromptDocument();
        
        // Process locally
        const result = fallbackAnalysis(unitCode, statement, promptDoc);
        
        if (result.success) {
          // Update fields
          updatePropertyFromAIResult(sheet, row, headers, result.data);
          
          // Update success states and remove failure
          updatePropertyStatus(sheet, row, headers[CONFIG.SUCCESS_STATES_COL], 'تفكيك');
          removePropertyStatus(sheet, row, headers[CONFIG.STATUS_COL], 'عقار فاشل');
          
          // Send success notification
          sendOrUpdateNotification(unitCode, 'تفكيك', {
            fallbackResult: result.data,
            row: row
          }, false);
          
        } else {
          // Send final failure notification
          sendOrUpdateNotification(unitCode, 'فشل نهائي', {
            error: 'Failed in both AI and fallback processing',
            row: row
          }, false);
        }
      }
    }
    
    console.log('Fallback processor completed');
    
  } catch (error) {
    console.error('Error in fallbackProcessor:', error);
    handleError(error, 'Fallback Processor');
  }
}

// ===== HELPER FUNCTIONS =====

/**
 * Handle multiple properties in a single submission
 */
function handleMultipleProperties(sheet, row, headers) {
  const multiValueField = sheet.getRange(row, headers[CONFIG.MULTI_VALUE_FIELD]).getValue();
  const statements = splitMultiValueField(multiValueField);
  
  console.log(`Found ${statements.length} properties in multi-value field`);
  
  // Process each statement
  statements.forEach((statement, index) => {
    if (index === 0) {
      // Update first row
      sheet.getRange(row, headers[CONFIG.SINGLE_VALUE_FIELD]).setValue(statement);
      markForAIProcessing(sheet, row, headers);
    } else {
      // Create new rows for additional statements
      const newRow = sheet.getLastRow() + 1;
      
      // Copy row data
      const sourceRange = sheet.getRange(row, 1, 1, sheet.getLastColumn());
      const targetRange = sheet.getRange(newRow, 1, 1, sheet.getLastColumn());
      sourceRange.copyTo(targetRange);
      
      // Update statement and generate new unit code
      sheet.getRange(newRow, headers[CONFIG.SINGLE_VALUE_FIELD]).setValue(statement);
      const newUnitCode = generateUnitCode(sheet, headers[CONFIG.UNIT_CODE_COL]);
      sheet.getRange(newRow, headers[CONFIG.UNIT_CODE_COL]).setValue(newUnitCode);
      
      // Send notification for new property
      sendOrUpdateNotification(newUnitCode, 'تخزين', {
        row: newRow,
        timestamp: new Date()
      }, true);
      
      markForAIProcessing(sheet, newRow, headers);
    }
  });
}

/**
 * Mark property for AI processing
 */
function markForAIProcessing(sheet, row, headers) {
  // Add marker or update status to indicate ready for AI
  const notes = sheet.getRange(row, headers[CONFIG.UNIT_CODE_COL]).getNote();
  sheet.getRange(row, headers[CONFIG.UNIT_CODE_COL]).setNote(notes + '\n[AI_READY]');
}

/**
 * Update property fields from AI result
 */
function updatePropertyFromAIResult(sheet, row, headers, aiData) {
  Object.keys(aiData).forEach(field => {
    if (headers[field]) {
      sheet.getRange(row, headers[field]).setValue(aiData[field]);
    }
  });
}

/**
 * Get prompt document from Google Docs
 */
function getPromptDocument() {
  try {
    const docId = PropertiesService.getScriptProperties().getProperty('PROMPT_DOC_ID');
    const doc = DocumentApp.openById(docId);
    return doc.getBody().getText();
  } catch (error) {
    console.error('Error getting prompt document:', error);
    return '';
  }
}

/**
 * Handle errors consistently
 */
function handleError(error, context) {
  console.error(`Error in ${context}:`, error);
  
  // Log to error sheet if exists
  try {
    const errorSheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName('Errors');
    if (errorSheet) {
      errorSheet.appendRow([
        new Date(),
        context,
        error.toString(),
        error.stack
      ]);
    }
  } catch (e) {
    console.error('Failed to log error:', e);
  }
}

// ===== MOCK FUNCTIONS FOR LOCAL TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    onFormSubmit,
    aiProcessor,
    fallbackProcessor
  };
}