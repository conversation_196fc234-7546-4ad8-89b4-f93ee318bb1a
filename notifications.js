/**
 * Notification system for Google Chat App Bot
 */

// ===== CONFIGURATION =====
const CHAT_CONFIG = {
  WEBHOOK_URL: '', // Will be set from Script Properties
  SPACE_ID: '',
  BOT_ID: '',
  API_ENDPOINT: 'https://chat.googleapis.com/v1/spaces/',
  NOTIFICATIONS_SHEET: 'notification log'
};

// ===== MAIN NOTIFICATION FUNCTIONS =====

/**
 * Send or update notification in Google Chat
 * @param {string} unitCode - Unit code (used as threadKey)
 * @param {string} status - Current status
 * @param {Object} details - Additional details
 * @param {boolean} isInitial - Is this the first notification
 */
function sendOrUpdateNotification(unitCode, status, details, isInitial = false) {
  try {
    console.log(`Sending/updating notification for ${unitCode} - Status: ${status}`);
    
    // Get notification log
    const logSheet = getNotificationLogSheet();
    const existingMessage = findExistingMessage(logSheet, unitCode);
    
    // Build message payload
    const payload = buildChatCardPayload(unitCode, status, details);
    
    let messageId;
    
    if (existingMessage && !isInitial) {
      // Update existing message
      messageId = updateChatMessage(existingMessage.messageId, payload, unitCode);
    } else {
      // Send new message
      messageId = sendNewChatMessage(payload, unitCode);
    }
    
    // Log the notification
    logNotification(logSheet, unitCode, messageId, status);
    
    console.log(`Notification sent/updated successfully: ${messageId}`);
    return messageId;
    
  } catch (error) {
    console.error('Error in sendOrUpdateNotification:', error);
    throw error;
  }
}

/**
 * Build chat card payload based on status and details
 * @param {string} unitCode - Unit code
 * @param {string} status - Current status
 * @param {Object} details - Additional details
 * @return {Object} Chat card payload
 */
function buildChatCardPayload(unitCode, status, details) {
  const timestamp = new Date().toLocaleString('ar-EG');
  let card;
  
  switch (status) {
    case 'تخزين':
      card = buildStorageCard(unitCode, details);
      break;
      
    case 'تفكيك':
      card = buildAnalysisCard(unitCode, details);
      break;
      
    case 'نجاح':
      card = buildSuccessCard(unitCode, details);
      break;
      
    case 'فشل':
    case 'فشل نهائي':
      card = buildFailureCard(unitCode, status, details);
      break;
      
    case 'تالف':
      card = buildDamagedCard(unitCode, details);
      break;
      
    default:
      card = buildGenericCard(unitCode, status, details);
  }
  
  return {
    cards: [card],
    text: `تحديث العقار ${unitCode} - ${status}`,
    threadKey: unitCode
  };
}

/**
 * Build storage notification card
 */
function buildStorageCard(unitCode, details) {
  const sheetUrl = getSheetUrl(details.row);
  
  return {
    header: {
      title: `📦 تم تخزين عقار جديد`,
      subtitle: unitCode,
      imageUrl: 'https://www.gstatic.com/images/icons/material/system/2x/storage_black_48dp.png'
    },
    sections: [{
      widgets: [
        {
          textParagraph: {
            text: `<b>الحالة:</b> تخزين أولي`
          }
        },
        {
          textParagraph: {
            text: `<b>التاريخ:</b> ${new Date().toLocaleString('ar-EG')}`
          }
        },
        {
          buttons: [{
            textButton: {
              text: 'عرض في Google Sheet',
              onClick: {
                openLink: {
                  url: sheetUrl
                }
              }
            }
          }]
        }
      ]
    }]
  };
}

/**
 * Build analysis notification card
 */
function buildAnalysisCard(unitCode, details) {
  const widgets = [
    {
      textParagraph: {
        text: `✅ <b>تم تحليل العقار بنجاح</b>`
      }
    }
  ];
  
  if (details.aiResult) {
    widgets.push({
      textParagraph: {
        text: `<b>نتائج التحليل:</b>`
      }
    });
    
    // Add key results
    const importantFields = ['المنطقة', 'نوع الوحدة', 'المساحة', 'السعر'];
    importantFields.forEach(field => {
      if (details.aiResult[field]) {
        widgets.push({
          textParagraph: {
            text: `• ${field}: ${details.aiResult[field]}`
          }
        });
      }
    });
  }
  
  return {
    header: {
      title: `🔍 تم تحليل العقار`,
      subtitle: unitCode
    },
    sections: [{
      widgets: widgets
    }]
  };
}

/**
 * Build success notification card
 */
function buildSuccessCard(unitCode, details) {
  const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(CONFIG.SHEET_NAME);
  const headers = getHeadersMap(sheet);
  const propertyData = sheet.getRange(details.row, 1, 1, sheet.getLastColumn()).getValues()[0];
  
  const widgets = [
    {
      textParagraph: {
        text: `✅ <b>تم تخزين عقار جديد بنجاح</b>`
      }
    },
    {
      keyValue: {
        topLabel: 'البيان',
        content: propertyData[headers['تفاصيل كاملة'] - 1] || 'غير محدد'
      }
    },
    {
      keyValue: {
        topLabel: 'اسم المالك',
        content: propertyData[headers['اسم المالك'] - 1] || 'غير محدد'
      }
    },
    {
      keyValue: {
        topLabel: 'رقم المالك',
        content: propertyData[headers['رقم المالك'] - 1] || 'غير محدد'
      }
    },
    {
      keyValue: {
        topLabel: 'حالة الصور',
        content: propertyData[headers['حالة الصور'] - 1] || 'غير محدد'
      }
    },
    {
      keyValue: {
        topLabel: 'إتاحة الوحدة',
        content: propertyData[headers['اتاحة العقار'] - 1] || 'غير محدد'
      }
    },
    {
      keyValue: {
        topLabel: 'كود الوحدة',
        content: unitCode
      }
    }
  ];
  
  // Add links
  const buttons = [];
  
  // Sheet link
  buttons.push({
    textButton: {
      text: 'Google Sheet',
      onClick: {
        openLink: {
          url: getSheetUrl(details.row)
        }
      }
    }
  });
  
  // Notion link (if available)
  if (details.notionUrl) {
    buttons.push({
      textButton: {
        text: 'Notion',
        onClick: {
          openLink: {
            url: details.notionUrl
          }
        }
      }
    });
  }
  
  // Zoho link (if available)
  if (details.zohoUrl) {
    buttons.push({
      textButton: {
        text: 'Zoho CRM',
        onClick: {
          openLink: {
            url: details.zohoUrl
          }
        }
      }
    });
  }
  
  widgets.push({
    buttons: buttons
  });
  
  return {
    header: {
      title: `✅ عقار ناجح`,
      subtitle: unitCode,
      imageUrl: 'https://www.gstatic.com/images/icons/material/system/2x/check_circle_black_48dp.png'
    },
    sections: [{
      widgets: widgets
    }]
  };
}

/**
 * Build failure notification card
 */
function buildFailureCard(unitCode, status, details) {
  const widgets = [
    {
      textParagraph: {
        text: `❌ <b>${status === 'فشل نهائي' ? 'فشل نهائي في المعالجة' : 'فشل في المعالجة'}</b>`
      }
    },
    {
      keyValue: {
        topLabel: 'كود الوحدة',
        content: unitCode
      }
    },
    {
      keyValue: {
        topLabel: 'سبب الفشل',
        content: details.error || 'غير محدد'
      }
    }
  ];
  
  // Add processing stages
  if (details.stage) {
    widgets.push({
      keyValue: {
        topLabel: 'المرحلة',
        content: details.stage
      }
    });
  }
  
  // Add raw statement if available
  if (details.rawStatement) {
    widgets.push({
      textParagraph: {
        text: `<b>البيان الخام:</b>`
      }
    });
    widgets.push({
      textParagraph: {
        text: details.rawStatement
      }
    });
  }
  
  return {
    header: {
      title: `❌ عقار فاشل`,
      subtitle: unitCode,
      imageUrl: 'https://www.gstatic.com/images/icons/material/system/
            imageUrl: 'https://www.gstatic.com/images/icons/material/system/2x/error_black_48dp.png'
    },
    sections: [{
      widgets: widgets
    }]
  };
}

/**
 * Build damaged property notification card
 */
function buildDamagedCard(unitCode, details) {
  const widgets = [
    {
      textParagraph: {
        text: `🗑️ <b>عقار تالف - حقول إلزامية ناقصة</b>`
      }
    },
    {
      keyValue: {
        topLabel: 'السبب',
        content: details.reason || 'حقول إلزامية ناقصة'
      }
    }
  ];
  
  if (details.missingFields) {
    widgets.push({
      textParagraph: {
        text: `<b>الحقول الناقصة:</b> ${details.missingFields.join(', ')}`
      }
    });
  }
  
  return {
    header: {
      title: `🗑️ عقار تالف`,
      subtitle: unitCode,
      imageUrl: 'https://www.gstatic.com/images/icons/material/system/2x/delete_black_48dp.png'
    },
    sections: [{
      widgets: widgets
    }]
  };
}

/**
 * Build generic notification card
 */
function buildGenericCard(unitCode, status, details) {
  return {
    header: {
      title: `تحديث: ${status}`,
      subtitle: unitCode
    },
    sections: [{
      widgets: [
        {
          textParagraph: {
            text: `<b>الحالة:</b> ${status}`
          }
        },
        {
          textParagraph: {
            text: `<b>التفاصيل:</b> ${JSON.stringify(details)}`
          }
        }
      ]
    }]
  };
}

/**
 * Send new chat message
 */
function sendNewChatMessage(payload, threadKey) {
  try {
    const token = getOAuthToken();
    const spaceId = PropertiesService.getScriptProperties().getProperty('CHAT_SPACE_ID');
    
    const url = `${CHAT_CONFIG.API_ENDPOINT}${spaceId}/messages?threadKey=${threadKey}`;
    
    const options = {
      method: 'post',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    };
    
    const response = UrlFetchApp.fetch(url, options);
    const result = JSON.parse(response.getContentText());
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Chat API error: ${result.error.message}`);
    }
    
    return result.name; // This is the messageId
    
  } catch (error) {
    console.error('Error sending chat message:', error);
    throw error;
  }
}

/**
 * Update existing chat message
 */
function updateChatMessage(messageId, payload, threadKey) {
  try {
    const token = getOAuthToken();
    const spaceId = PropertiesService.getScriptProperties().getProperty('CHAT_SPACE_ID');
    
    const url = `${CHAT_CONFIG.API_ENDPOINT}${spaceId}/messages/${messageId}?updateMask=cards,text`;
    
    const options = {
      method: 'patch',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      payload: JSON.stringify(payload),
      muteHttpExceptions: true
    };
    
    const response = UrlFetchApp.fetch(url, options);
    const result = JSON.parse(response.getContentText());
    
    if (response.getResponseCode() !== 200) {
      throw new Error(`Chat API error: ${result.error.message}`);
    }
    
    return messageId;
    
  } catch (error) {
    console.error('Error updating chat message:', error);
    // If update fails, send new message
    return sendNewChatMessage(payload, threadKey);
  }
}

/**
 * Get OAuth token for Chat API
 */
function getOAuthToken() {
  // Using service account credentials
  const serviceAccount = JSON.parse(PropertiesService.getScriptProperties().getProperty('SERVICE_ACCOUNT_KEY'));
  
  const now = Math.floor(Date.now() / 1000);
  const payload = {
    iss: serviceAccount.client_email,
    scope: 'https://www.googleapis.com/auth/chat.bot',
    aud: 'https://oauth2.googleapis.com/token',
    exp: now + 3600,
    iat: now
  };
  
  const jwt = Utilities.base64EncodeWebSafe(JSON.stringify({alg: 'RS256', typ: 'JWT'})) + '.' +
             Utilities.base64EncodeWebSafe(JSON.stringify(payload));
  
  const signature = Utilities.computeRsaSha256Signature(jwt, serviceAccount.private_key);
  const token = jwt + '.' + Utilities.base64EncodeWebSafe(signature);
  
  const response = UrlFetchApp.fetch('https://oauth2.googleapis.com/token', {
    method: 'post',
    payload: {
      grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
      assertion: token
    }
  });
  
  const result = JSON.parse(response.getContentText());
  return result.access_token;
}

/**
 * Get notification log sheet
 */
function getNotificationLogSheet() {
  const logSheetId = PropertiesService.getScriptProperties().getProperty('NOTIFICATIONS_LOG_SHEET_ID');
  const logSpreadsheet = SpreadsheetApp.openById(logSheetId);
  let logSheet = logSpreadsheet.getSheetByName(CHAT_CONFIG.NOTIFICATIONS_SHEET);
  
  // Create if doesn't exist
  if (!logSheet) {
    logSheet = logSpreadsheet.insertSheet(CHAT_CONFIG.NOTIFICATIONS_SHEET);
    logSheet.appendRow(['UnitCode', 'MessageId', 'LastUpdated', 'Status']);
  }
  
  return logSheet;
}

/**
 * Find existing message in log
 */
function findExistingMessage(logSheet, unitCode) {
  const data = logSheet.getDataRange().getValues();
  
  for (let i = 1; i < data.length; i++) {
    if (data[i][0] === unitCode) {
      return {
        row: i + 1,
        messageId: data[i][1],
        lastUpdated: data[i][2],
        status: data[i][3]
      };
    }
  }
  
  return null;
}

/**
 * Log notification
 */
function logNotification(logSheet, unitCode, messageId, status) {
  const existingMessage = findExistingMessage(logSheet, unitCode);
  const timestamp = new Date();
  
  if (existingMessage) {
    // Update existing
    logSheet.getRange(existingMessage.row, 2, 1, 3).setValues([[
      messageId,
      timestamp,
      status
    ]]);
  } else {
    // Add new
    logSheet.appendRow([unitCode, messageId, timestamp, status]);
  }
}

/**
 * Get sheet URL with row
 */
function getSheetUrl(row) {
  const sheetId = PropertiesService.getScriptProperties().getProperty('SHEET_ID');
  return `https://docs.google.com/spreadsheets/d/${sheetId}/edit#gid=52450265&range=A${row}`;
}

// ===== MOCK FUNCTIONS FOR LOCAL TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    sendOrUpdateNotification,
    buildChatCardPayload,
    getNotificationLogSheet,
    findExistingMessage,
    logNotification
  };
}