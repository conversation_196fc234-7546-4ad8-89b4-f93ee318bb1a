/**
 * Utility functions for the Real Estate Processing System
 */

// ===== AI PLATFORM CONFIGURATIONS =====
const AI_PLATFORMS = {
  OPENAI: {
    name: 'OpenAI',
    endpoint: 'https://api.openai.com/v1/chat/completions',
    keyProperty: 'OPENAI_API_KEY',
    model: 'gpt-4'
  },
  MISTRAL: {
    name: 'Mistral',
    endpoint: 'https://api.mistral.ai/v1/chat/completions',
    keyProperty: 'MISTRAL_API_KEY',
    model: 'mistral-large'
  },
  GROQ: {
    name: 'Groq',
    endpoint: 'https://api.groq.com/openai/v1/chat/completions',
    keyProperty: 'GROQ_API_KEY',
    model: 'llama3-70b-8192'
  },
  GEMINI: {
    name: 'Google Gemini',
    endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/',
    keyProperty: 'GEMINI_API_KEY',
    model: 'gemini-pro'
  }
};

// ===== UNIT CODE GENERATION =====

/**
 * Generate unit code in format DD.MM.YYYY-NNN
 * @param {Sheet} sheet - Google Sheet object
 * @param {number} unitCodeColIndex - Unit code column index
 * @return {string} Generated unit code
 */
function generateUnitCode(sheet, unitCodeColIndex) {
  const today = new Date();
  const dateStr = Utilities.formatDate(today, 'GMT+2', 'dd.MM.yyyy');
  
  // Find last code for today
  let lastSerial = 0;
  const data = sheet.getDataRange().getValues();
  
  for (let i = 1; i < data.length; i++) {
    const code = data[i][unitCodeColIndex - 1];
    if (code && code.startsWith(dateStr)) {
      const serial = parseInt(code.split('-')[1]);
      if (serial > lastSerial) {
        lastSerial = serial;
      }
    }
  }
  
  // Generate new code
  const newSerial = String(lastSerial + 1).padStart(3, '0');
  return `${dateStr}-${newSerial}`;
}

// ===== DATA PROCESSING =====

/**
 * Split multi-value field into separate statements
 * @param {string} multiValueField - Field containing multiple values
 * @return {Array} Array of individual statements
 */
function splitMultiValueField(multiValueField) {
  if (!multiValueField) return [];
  
  // Split by empty lines or lines with only symbols
  const lines = multiValueField.split('\n');
  const statements = [];
  let currentStatement = [];
  
  for (const line of lines) {
    const trimmedLine = line.trim();
    
    // Check if line contains only symbols (no letters or numbers)
    const hasAlphanumeric = /[a-zA-Z0-9\u0600-\u06FF]/.test(trimmedLine);
    
    if (!trimmedLine || !hasAlphanumeric) {
      // End of statement
      if (currentStatement.length > 0) {
        statements.push(currentStatement.join('\n').trim());
        currentStatement = [];
      }
    } else {
      // Part of statement
      currentStatement.push(trimmedLine);
    }
  }
  
  // Add last statement if exists
  if (currentStatement.length > 0) {
    statements.push(currentStatement.join('\n').trim());
  }
  
  return statements.filter(s => s.length > 0);
}

/**
 * Get headers map from sheet
 * @param {Sheet} sheet - Google Sheet object
 * @return {Object} Headers map with column indices
 */
function getHeadersMap(sheet) {
  const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
  const headersMap = {};
  
  headers.forEach((header, index) => {
    if (header) {
      headersMap[header.trim()] = index + 1;
    }
  });
  
  return headersMap;
}

// ===== AI PROCESSING =====

/**
 * Process property with AI platforms
 * @param {string} unitCode - Unit code
 * @param {string} statement - Property statement
 * @return {Object} Processing result
 */
function processWithAI(unitCode, statement) {
  console.log(`Processing ${unitCode} with AI...`);
  
  const prompt = getPromptFromDocs();
  const platforms = Object.keys(AI_PLATFORMS);
  
  // Try each platform up to 3 times
  for (let attempt = 1; attempt <= 3; attempt++) {
    console.log(`Attempt ${attempt} of 3`);
    
    for (const platformKey of platforms) {
      const result = tryAIPlatform(platformKey, statement, prompt);
      
      if (result.success) {
        console.log(`Success with ${AI_PLATFORMS[platformKey].name}`);
        return result;
      }
    }
  }
  
  return {
    success: false,
    error: 'All AI platforms failed after 3 attempts'
  };
}

/**
 * Try processing with specific AI platform
 */
function tryAIPlatform(platformKey, statement, prompt) {
  try {
    const platform = AI_PLATFORMS[platformKey];
    const apiKey = PropertiesService.getScriptProperties().getProperty(platform.keyProperty);
    
    if (!apiKey) {
      console.log(`No API key for ${platform.name}`);
      return { success: false, error: 'No API key' };
    }
    
    let response;
    
    if (platformKey === 'GEMINI') {
      response = callGeminiAPI(apiKey, platform, statement, prompt);
    } else {
      response = callChatAPI(apiKey, platform, statement, prompt);
    }
    
    if (response.success) {
      const parsedData = parseAIResponse(response.content);
      return {
        success: true,
        data: parsedData,
        platform: platform.name
      };
    }
    
    return response;
    
  } catch (error) {
    console.error(`Error with ${platformKey}:`, error);
    return { success: false, error: error.toString() };
  }
}

/**
 * Call standard chat completion API
 */
function callChatAPI(apiKey, platform, statement, prompt) {
  const payload = {
    model: platform.model,
    messages: [
      { role: 'system', content: prompt },
      { role: 'user', content: statement }
    ],
    temperature: 0.3
  };
  
  const options = {
    method: 'post',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };
  
  const response = UrlFetchApp.fetch(platform.endpoint, options);
  const result = JSON.parse(response.getContentText());
  
  if (response.getResponseCode() === 200) {
    return {
      success: true,
      content: result.choices[0].message.content
    };
  }
  
  return {
    success: false,
    error: result.error?.message || 'API error'
  };
}

/**
 * Call Gemini API
 */
function callGeminiAPI(apiKey, platform, statement, prompt) {
  const url = `${platform.endpoint}${platform.model}:generateContent?key=${apiKey}`;
  
  const payload = {
    contents: [{
      parts: [{
        text: prompt + '\n\n' + statement
      }]
    }],
    generationConfig: {
      temperature: 0.3
    }
  };
  
  const options = {
    method: 'post',
    headers: {
      'Content-Type': 'application/json'
    },
    payload: JSON.stringify(payload),
    muteHttpExceptions: true
  };
  
  const response = UrlFetchApp.fetch(url, options);
  const result = JSON.parse(response.getContentText());
  
  if (response.getResponseCode() === 200) {
    return {
      success: true,
      content: result.candidates[0].content.parts[0].text
    };
  }
  
  return {
    success: false,
    error: result.error?.message || 'Gemini API error'
  };
}

/**
 * Parse AI response to extract property data
 */
function parseAIResponse(content) {
  try {
    // Try to parse as JSON first
    const jsonMatch = content.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    
    // Parse key-value format
    const data = {};
    const lines = content.split('\n');
    
    for (const line of lines) {
      const match = line.match(/^(.+?):\s*(.+)$/);
      if (match) {
        const key = match[1].trim();
        const value = match[2].trim();
        data[key] = value;
      }
    }
    
    return data;
    
  } catch (error) {
    console.error('Error parsing AI response:', error);
    return {};
  }
}

// ===== FALLBACK ANALYSIS =====

/**
 * Fallback analysis using JavaScript
 * @param {string} unitCode - Unit code
 * @param {string} statement - Property statement
 * @param {string} promptDoc - Prompt document content
 * @return {Object} Analysis result
 */
function fallbackAnalysis(unitCode, statement, promptDoc) {
  console.log(`Fallback analysis for ${unitCode}`);
  
  try {
    const extractedData = {
      'تفاصيل كاملة': statement
    };
    
    // Extract area patterns
    const areaPatterns = [
      /المنطقة[:\s]*([^\s,]+)/,
      /منطقة[:\s]*([^\s,]+)/,
      /(التجمع|قطامية|النزهة|الأندلس|جاردينيا)/
    ];
    
    for (const pattern of areaPatterns) {
      const match = statement.match(pattern);
      if (match) {
        extractedData['المنطقة'] = match[1];
        break;
      }
    }
    
    // Extract price
    const pricePatterns = [
      /السعر[:\s]*(\d+)/,
      /(\d+)\s*جنيه/,
      /(\d+)\s*ج\.م/
    ];
    
    for (const pattern of pricePatterns) {
      const match = statement.match(pattern);
      if (match) {
        extractedData['السعر'] = parseInt(match[1]);
        break;
      }
    }
    
    // Extract area (size)
    const sizePatterns = [
      /المساحة[:\s]*(\d+)/,
      /(\d+)\s*متر/,
      /(\d+)\s*م/
    ];
    
    for (const pattern of sizePatterns) {
      const match = statement.match(pattern);
      if (match) {
        extractedData['المساحة'] = parseInt(match[1]);
        break;
      }
    }
    
    // Extract unit type
    const unitTypes = {
      'شقة': ['شقة', 'شقه'],
      'فيلا': ['فيلا', 'فيلا'],
      'مكتب': ['مكتب', 'اوفيس'],
      'عيادة': ['عيادة', 'عياده']
    };
    
    for (const [type, keywords] of Object.entries(unitTypes)) {
      for (const keyword of keywords) {
        if (statement.includes(keyword)) {
          extractedData['نوع الوحده'] = type;
          break;
        }
      }
    }
    
    // Extract phone number
    const phonePattern = /01[0-2,5]\d{8}/;
    const phoneMatch = statement.match(phonePattern);
    if (phoneMatch) {
      extractedData['رقم المالك'] = phoneMatch[0];
    }
    
    // Extract owner name (before phone number)
    if (phoneMatch) {
      const beforePhone = statement.substring(0, phoneMatch.index).trim();
      const nameMatch = beforePhone.match(/([أ-ي\s]+)$/);
      if (nameMatch) {
        extractedData['اسم المالك'] = nameMatch[1].trim();
      }
    }
    
    return {
      success: true,
      data: extractedData
    };
    
  } catch (error) {
    console.error('Fallback analysis error:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Get prompt from Google Docs
 */
function getPromptFromDocs() {
  try {
    const docId = PropertiesService.getScriptProperties().getProperty('PROMPT_DOC_ID');
    if (!docId) {
      return getDefaultPrompt();
    }
    
    const doc = DocumentApp.openById(docId);
    return doc.getBody().getText();
    
  } catch (error) {
    console.error('Error getting prompt from docs:', error);
    return getDefaultPrompt();
  }
}

/**
 * Get default prompt
 */
function getDefaultPrompt() {
  return `أنت محلل بيانات عقارية محترف. مهمتك هي استخراج المعلومات من النص المعطى وتنظيمها.

المعلومات المطلوبة:
- المنطقة
- نوع الوحدة (شقة، فيلا، مكتب، إلخ)
- حالة الوحدة (فاضي، مفروش، تمليك)
- المساحة (رقم فقط)
- السعر (رقم فقط)
- الدور
- اسم المالك
- رقم المالك (11 رقم)
- العنوان
- إتاحة العقار (متاح، غير متاح، مؤجر)
- حالة الصور (بصور، بدون صور)

قم بإرجاع البيانات في صيغة JSON.`;
}

// ===== ERROR HANDLING =====

/**
 * Safe property getter
 */
function getScriptProperty(key, defaultValue = '') {
  try {
    return PropertiesService.getScriptProperties().getProperty(key) || defaultValue;
  } catch (error) {
    console.error(`Error getting property ${key}:`, error);
    return defaultValue;
  }
}

// ===== MOCK FUNCTIONS FOR LOCAL TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  // Mock Google Apps Script services for local testing
  global.SpreadsheetApp = {
    getActiveSpreadsheet: () => ({
      getSheetByName: () => ({
        getLastRow: () => 10,
        getLastColumn: () => 20,
        getRange: () => ({
          getValue: () => 'test value',
          setValue: () => {},
          getValues: () => [[]],
          setValues: () => {},
          getNote: () => '',
          setNote: () => {}
        }),
        getDataRange: () => ({
          getValues: () => [[]]
        }),
        appendRow: () => {}
      })
    })
  };
  
  global.PropertiesService = {
    getScriptProperties: () => ({
      getProperty: () => 'test_property'
    })
  };
  
  global.Utilities = {
    formatDate: () => '01.01.2024'
  };
  
  global.Logger = {
    log: console.log
  };
  
  module.exports = {
    generateUnitCode,
    splitMultiValueField,
    getHeadersMap,
    processWithAI,
    fallbackAnalysis,
    getPromptFromDocs,
    getScriptProperty
  };
}