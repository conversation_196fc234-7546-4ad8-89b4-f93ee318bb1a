فيما يلي النسخة الموحدة والمحدَّثة لوثيقة «تصنيف العقارات وآلية التعامل معها» بعد دمج جميع التغييرات المطلوبة، بما فيها انتقال الإشعارات إلى Google Chat App Bot، وإعادة ترتيب مراحل المعالجة، وتعريف الحقول الإلزامية والافتراضية، وكود الوحدة الجديد، ومنطق منع التكرار.


1. مسار البيانات – المرحلة الأولى
1.1 استقبال البيان
• النموذج: «ضع بيان عقارك هنا» → ورقة Aqar_bot في ملف «Elsweedy real estate sheet».
• يُنشئ كل رد صفًّا جديدًا.
• تُضاف القيمة «تخزين» إلى حقل «حالات النجاح».

1.2 توليد كود الوحدة (DD.MM.YYYY-NNN)
• يُقرأ آخر كود لليوم نفسه، ثم يُزاد الرقم.
• يُكتب في حقل «كود الوحدة» فور إنشاء الصف.
1.3 الإشعار الأولي – Google Chat
• يُستدعى ‎sendOrUpdateNotification‎ بكود الوحدة كـ threadKey.
• حالة الإشعار = «تخزين».
• يُسجَّل ‎messageId‎ وكود الوحدة في شيت ‎notification log‎.


1. الحقول وقواعد القيم الافتراضية
النصوص → «غير محدد» | الأرقام → 0
الحقول الإلزامية منطقيًّا: «المنطقة»، «نوع الوحدة»، «حالة الوحدة».
إذا خلت الحقول الثلاثة أو كانت «غير محدد» → يُصنَّف العقار «تالف» لكن يستمر المسار.



1. تصنيف العقار
3.1 حالة المعالجة (Processing State)
• ✅ عقار ناجح – أتمّ جميع الخطوات (AI → Notion/Zoho).
• ❌ عقار فاشل – توقّف في أي خطوة.

3.2 الأنواع الفرعية للنجاح
• 🏢 عقار متعدد – رقم مالك متكرر.
• 🔁 عقار مكرّر – تطابق تام مع عقار سابق.
• 🗑️ عقار تالف – نقص حقول إلزامية منطقيًّا.
• ✅ عقار عادي – لا ينتمي لأيّ مما سبق.


1. منطق التصنيف (بعد اكتمال المعالجة)


1. هل فشل جزئي/كامل؟ نعم → فاشل.
2. نجاح كامل → انتقل للفروع:
 أ. حقول إلزامية ناقصة؟ → تالف.
 ب. رقم مالك متكرر؟   → متعدد.
 ج. تطابق تام؟     → مكرّر.
 د. خلاف ذلك     → عادي.



1. نظام الإشعارات – Google Chat Bot
5.1 الإرسال أو التحديث ‎sendOrUpdateNotification(unitCode, status, details, isInitial)‎
• تبحث عن unitCode في «notification log».
• إن وُجد ‎messageId‎ → تُحدّث الرسالة (PATCH)؛ وإلا تُنشئ رسالة جديدة (POST).
• ‎threadKey = unitCode‎.

5.2 محتوى الرسائل
أ) الإشعار الأولي («تخزين»)
 • كود الوحدة + العنوان.
 • حالة «تخزين».
 • رابط الصفّ في شيت البيانات.
ب) إشعار نجاح نهائي
 • ✅ «تم تخزين عقار جديد بنجاح».
 • الملخص الكامل (بيان، مالك، حالة الصور، إتاحة، كود…).
 • روابط: صفّ Google Sheet (مع رقم الصف)، صفحة Notion، سجل Zoho (يُرسل فقط عند نجاح إنشائها).
ج) إشعار فشل
 • ❌ «عقار فاشل».
 • كود الوحدة، سبب الفشل، المراحل المنجزة ✔/✖.
 • البيان الخام.
 • لا يُرسل إشعار فشل مكرر إلا إذا تغيّر رمز الفشل المحفوظ في الشيت.


1. جداول المراقبة
6.1 ‎notification log (شيت)
| UnitCode | MessageId | LastUpdated | Status |

6.2 ‎Elsweedy real estate sheet‎
• تُحدَّث الأعمدة: حالة، حالات النجاح، تاريخ آخر معالجة.


1. بنية السكربتات – Google Apps Script (V8)
ملفات المشروع:
• main.js     → ‎onFormSubmit(e)‎, ‎aiProcessor()‎, ‎fallbackProcessor()‎
• validation.js → ‎setDefaultValues‎, ‎checkAndMarkDamaged‎, ‎updatePropertyStatus‎
• notifications.js→ ‎sendOrUpdateNotification‎, ‎buildChatCardPayload‎
• utils.js     → ‎generateUnitCode‎, ‎splitMultiValueField‎, ‎getHeadersMap‎



1. المعالجات
8.1 ‎onFormSubmit(e)‎
 1) قراءة الصف.
 2) ‎setDefaultValues‎.
 3) ‎generateUnitCode‎.
 4) ‎sendOrUpdateNotification‎ (isInitial=true).
 5) وضع «تخزين» في «حالات النجاح».

8.2 ‎aiProcessor()‎ (تعمل بوقت مُجدول)
 • تقرأ الصفوف غير المفكّكة.
 • تحاول عبر OpenAI → Mistral → … (حتى 5 منصات، 3 دورات).
 • عند النجاح → تضيف «تفكيك» في «حالات النجاح» وتُحدّث الإشعار.
 • عند الفشل الكامل → تُحدّث الإشعار برسالة الاستفهام «🚫 تعذر تحليل…».
8.3 ‎fallbackProcessor()‎
 • تُشغَّل بعد aiProcessor.
 • تحلل محليًّا باستخدام البرومبت من ملف ‎Flexible Real Estate Analysis Prompt‎.
 • تُحدّث الحقول، حالات النجاح، والإشعارات وفق النتيجة.


1. كود العقار التسلسلي الجديد (إن احتجته مرحلة لاحقة)
000-{نوع الحالة رقمياً}-{رقم المنطقة}-{DDMMYY}-{Serial}
(تُزال الحروف اللاتينية والفواصل، وتستبدل كلها بشرطات).



1. خصائص السكربت (Script Properties)
GOOGLE_FORM_ID
SHEET_ID
CHAT_APP_ID
CHAT_SPACE_ID
NOTIFICATIONS_LOG_SHEET_ID
OPENAI_API_KEY, … (بقية مفاتيح AI)


بهذا أصبحت الوثيقة متوافقة مع جميع التعديلات الأخيرة ومتسقة مع هيكل المشروع المقسَّم إلى أربعة ملفات JavaScript قابلة للنشر مباشرة على بيئة ‎Google Apps Script (V8).[{'index': 1}]
أنا عايز أعمل مشروع كامل بلغة Google Apps 
Script.
المشروع يبقى متقسم لأكتر من ملف JavaScript بالشكل التالي:

main.js للكود الرئيسي والـ Entry Point

validation.js لكل دوال التحقق والفحص

notifications.js لكل ما يخص الإشعارات

utils.js لدوال المساعدة العامة

كل ملف يكون Self-contained وجاهز للرفع على Google Apps Script كملف منفصل.
وعايز المشروع يكون مكتوب بصيغة JavaScript ES5/ES6 بحيث يشتغل مباشرة على Google Apps Script V8 runtime،
مع Mock functions محلية للتجربة على الكمبيوتر قبل الرفع،
بحيث أقدر أشغل المشروع محلي على Node.js للتجربة من غير ما يتصل بجوجل،
 وبعدين أرفعه على Apps Script بعد التأكد من أنه شغال."
الموقع 
المرحله الاولي من المشروع موجوده علي الملف
مراحل تسجيل العقار

عايز اعمل مشروع
 gas مقسم  الي مراحل
نبدا بالمرحله الاولي 
فورم جوجل اسمه 
اسم الفورم : ضع بيان عقارك هنا 
اسم المجلد :  Elsweedy real estate data 
# Google Forms Configuration
GOOGLE_FORM_ID=1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw
GOOGLE_FORM_URL=
https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/viewform
=========================================
اسماء الاقسام 
اسم السؤال الخاص بالبيانات المتعدده : 🪀 بيانات  متعددة  غير مفصله
==========
اسم السؤال الخاص بالبيانات المتعدده : 🪀  بيان  عقار  غير مفصل  
==========

اسم القسم الخاص بها : بيان  مفصل  💘
اسماء الخقول وانواعها والقيم الخاصه بها

المنطقة :  اسم الحقل
النوع : select 
الخيارات : اسكان شباب - سكن شباب - قطاميه - نزهه ثالث - حي اندلس - دار اندلس  -  جنه - جاردينيا هايتس - احياء تجمع
 - مستثمرين - شويفات - مستقبل - هناجر - سكن معارض - سكن اندلس  - نرجس عمارات - نرجس فيلات - لوتس  - زبرينا - بيت وطن - تجمع خامس - دار قرنفل - بنفسج عمارات - بنفسج فيلات - كمباوندات  - ياسمين -  
القيمه الافتراضيه : غير محدد
==================
حالة الصور: اسم الحقل
النوع: Select 
الخيارات : بصور  -  بدون صور 
القيمه الافتراضيه : غير محدد
==================
Status : اسم الحقل
النوع : Multi-select 
الخيارات : عقار مكتب  - عقار متعدد  - عقار تالف -  عقار مكرر - عقار ناجح-  عقار فاشل  
القيمه الافتراضيه : غير محدد
==================
اتاحة العقار : اسم الحقل
النوع : select 
الخيارات : اتباعت  - متاح  - مؤجر - غير متاح - عقار ناجح-  هتفضي في تاريخ  - معندوش نيه حاليا  - غير محدد - اتأجرت عن طريق المكتب - المالك لا يرد - الرقم مغلق - الرقم مشغول
القيمه الافتراضيه : غير محدد
==================
حالة الوحدة : اسم الحقل
النوع : Multi-select 
الخيارات : نصف مفروش - تمليك - فاضي -  مفروش - طبي- اداري - غير محدد
القيمه الافتراضيه : غير محدد
==================
اسم الموظف :  اسم الحقل
النوع : select 
الخيارات : اسلام - تاحه - بلبل - ايمن -  علياء - محمود سامي - يوسف عماد - غير محدد - يوسف الجوهري - 
القيمه الافتراضيه : غير محدد
==================
نوع الوحده :  اسم الحقل
النوع : select 
الخيارات : شقه - غرفه - مكتب - مخزن -  عياده -  عماره - فيلا - بنتا هاوس - توين هاوس - روف  - بيزمنت - 
القيمه الافتراضيه : غير محدد
==================
الدور   :  اسم الحقل
النوع : Multi-select 
الخيارات : بيزمنت - أرضي مرتفع  - أرضي - دور أول -  دور ثاني - دور ثالث  - دور رابع  - دور خامس - دور سادس - دور سابع - دور ثامن - متكرر  -  
القيمه الافتراضيه : غير محدد
==================
 كود الوحدة   :  اسم الحقل
النوع : text
يتم تعيين القيمه من قبل النظام 
==================
المساحة  :  اسم الحقل
النوع  : number 
القيمه الافتراضيه : 000
==================
السعر  :  اسم الحقل
النوع  : number 
القيمه الافتراضيه : 000
==================
تفاصيل كاملة:  اسم الحقل
النوع : text-area 
القيمه الافتراضيه : غير محدد
==================
العنوان  :  اسم الحقل
النوع : text
القيمه الافتراضيه : غير محدد
==================
تاريخ آخر معالجة :  اسم الحقل
النوع : Date 
يتم تعيين القيمه من قبل النظام 
==================
تاريخ الانشاء  :  اسم الحقل
النوع : Date 
يتم تعيين القيمه من قبل النظام 
==================
تاريخ التذكير :  اسم الحقل
النوع : Date 
القيمه الافتراضيه :  
==================
 اسم المالك :  اسم الحقل
النوع : text
القيمه الافتراضيه : غير محدد
==================
 رقم المالك :  اسم الحقل
النوع : number 
القيمه الافتراضيه : 01000000000
=====================================
بيانات صفحه جوجل شيت الرئيسيه
اسم الملف : Elsweedy real estate sheet
اسم المجلد :  Elsweedy real estate data 
اسم الورقه المرتبطه بالفورم : Aqar_bot
رابط الملف : https://docs.google.com/spreadsheets/d/1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c/edit?gid=52450265#gid=52450265
id : 1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c
 
 اسماء الحقول
< طابع زمني
🪀 بيانات  متعددة  غير مفصله
🪀  بيان  عقار  غير مفصل 
نوع البيان اللي بتسجله     ❓❔
اسم الموظف
الدور
السعر
إتاحة العقار
العنوان	
المساحة	
المنطقة
حالة الصور	حالة الوحدة	
Status
تاريخ التذكير
تاريخ تسجيل العقار في الجروب
اسم المالك	
رقم المالك	
تفاصيل كاملة
 كود الوحدة
========  
حالات النجاح : اسم الحقل
النوع : Multi-select 
الخيارات : تخزين - تفكيك - نوشن - زوهو -  اشعار
===============================
Google Form  بيسجل ردوده في ملف رئيسي (الشيت الأساسي  بيانات عقاريه غير مفصله
Google Apps Script Trigger يشتغل تلقائي عند كل رد جديد
 السكربت يعمل علي  عده احتمالات 
الاحتمال الاولي 
يقرأ الصف الجديد بالكامل
يلتقط قيمه حقل : نوع البيان اللي بتسجله     ❓❔ 	
لو وجد قيمه الحقل : 🪀 بيانات  متعددة  غير مفصله
ينظف الحقل متعدد القيم ويقسمه على صفوف منفصلة، .
بحيث يضيف القيمه الجديده في صفوف مختلف في الحقل المسمي 🪀  بيان  عقار  غير مفصل 
"القيم المتعددة يمكن فصلها بواسطة سطر فارغ، أو سطر يحتوي على أي رموز غير الحروف الأبجدية او الارقام  (مثل علامات ترقيم، أو رموز خاصة).بمعني  الأسطر التي تحتوي فقط على حروف او ارقام  (بدون رموز) تنتمي إلى نفس البيان، وأي فاصل بينهما يعتبر نهاية بيان وبداية بيان جديد."
و يولّد كود الوحدة  لكل قيمة وتوضع في حقل كود الوحدة
يتم ارسال اشعار الي جوجل شات بنجاح التخزين وسياتي تفصيل بخصوص هذا الموضوع بالتفاصيل
ثم يتم ارسال البيان الناتج الي منصات الذكاء الاجتماعي لتحليلها وتفكيكها الي كلمات دلاليه وحقول وسياتي تفصيل ذالك التحليل بالتفاصيل
في حاله النجاح يتم تخزين القيم المفككه في الحقول المطابقه لها
 و يتم ارسال اشعار الي جوجل شات بنجاح التحليل وسياتي تفصيل بخصوص هذا الموضوع بالتفاصيل
=====================

الاحتمال الثاني 
يقرأ الصف الجديد بالكامل
يلتقط قيمه حقل : نوع البيان اللي بتسجله     ❓❔ 	
لو وجد قيمه الحقل : 🪀  بيان  عقار  غير مفصل
يولّد كود الوحدة  لهذا البيان وتوضع في حقل كود الوحدة
يتم ارسال اشعار الي جوجل شات بنجاح التخزين وسياتي تفصيل بخصوص هذا الموضوع بالتفاصيل
ثم يتم ارسال البيان الي منصات الذكاء الاجتماعي لتحليلها وتفكيكها الي كلمات دلاليه وحقول وسياتي تفصيل ذالك التحليل بالتفاصيل
في حاله النجاح يتم تخزين القيم المفككه في الحقول المطابقه لها
 و يتم ارسال اشعار الي جوجل شات بنجاح التحليل  وسياتي تفصيل بخصوص هذا الموضوع بالتفاصيل
==========================

الاحتمال  الثالث 
يقرأ الصف الجديد بالكامل
يلتقط قيمه حقل : نوع البيان اللي بتسجله     ❓❔ 	
لو وجد قيمه الحقل : بيان  مفصل  💘
يولّد كود الوحدة  لهذا البيان وتوضع في حقل كود الوحدة
يتم ارسال اشعار الي جوجل شات بنجاح التخزين والتحليل  وسياتي تفصيل بخصوص هذا الموضوع بالتفاصيل
=========================================
تفاصيل جوجل اب سكريبت
aqar-bot-467120-aa84f57a4bae=********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************1Q
=========================================
كيفيه انشاء كود الوحده
📌 شكل كود الوحدة المطلوب:
DD.MM.YYYY-001
DD.MM.YYYY = تاريخ اليوم.

بعده شرطة -.

ثم رقم تسلسلي يومي يبدأ من 001 ويزيد لكل وحدة جديدة في نفس اليوم.

في اليوم التالي، يُعاد العداد إلى 001.
⚙️ منطق العمل الجديد :
يقرأ العمود المخصص لـ"كود الوحدة" في Google Sheet.

يجيب آخر صف فيه كود (أو آخر كود بنفس تاريخ اليوم).

يفك الكود بالشكل DD.MM.YYYY-NNN.

لو التاريخ  = النهاردة → يزيد الرقم.

لو التاريخ ≠ النهاردة → يبدأ من 001.

يكتب الكود الجديد في الصف التالي.
=========================================
تفاصيل منصات الذكاء الاصطناعي

# ===== إعدادات الذكاء الاصطناعي =====

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4

# Mistral AI
MISTRAL_API_KEY=soMr4s2jGPzGrKO00BOjOh7Vrhb5IxMP
MISTRAL_MODEL=mistral-large

# Groq
GROQ_API_KEY=********************************************************
GROQ_MODEL=llama3-70b-8192

# HuggingFace
HUGGINGFACE_API_KEY=*************************************
HUGGINGFACE_MODEL=meta-llama/Llama-2-70b-chat-hf
# Google Gemini
#====================================
GEMINI_API_KEY=AIzaSyC6hgtBF5ILBI8WGOmjavjEO3b699cL8A8
#====================================
GEMINI_API_KEY=AIzaSyDIEBBrQLsdJO3tIELc0zFuJVk1efZOo9c
#====================================
GEMINI_API_KEY=AIzaSyDboufcqfd5iVhmO3eRogqA4e3FUJz_fw8
========================================
بيان تحليل النصوص علي  منصات الذكاء الاصطناعي
او باستخدام كود جافا سكريبت
 بدأ بمحاولة تحليل البيان باستخدام أول منصة ذكاء صناعي، باستخدام البرومبت الموجود في صفحة القواعد (Google Docs).
إذا حصلت على نتيجة صالحة:
توقف فورًا.
أرسل النتيجة.
إذا فشلت المنصة:
جرّب التالية، وهكذا حتى 5 منصات.
محاولة التحليل باستخدام Gemini (3 مرات بمفاتيح مختلفة
إذا فشل الجميع:
كرر العملية الكاملة 3 مرات (بحد أقصى).
إذا فشلت جميع المحاولات بعد 3 دورات:
أرسل إشعارًا للمستخدم بهذا الفشل.
اسأله إن كان يريد المتابعة عبر Google Apps Script/JavaScript.
إذا وافق:
أرسل البيان للكود المحلي ليحلله بنفس البرومبت الموجود في صفحة القواعد.
إذا رفض:
أبلغه بعدم تنفيذ أي تحليل.
=========================================
بيانات صفحه الاشعارات 
اسم الملف : notification log
اسم المجلد :  Elsweedy real estate data 
رابط الملف : https://docs.google.com/spreadsheets/d/1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM/edit?gid=0#gid=0 
id : 1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c
=========================================
اعدادات الاشعارات 

أيها الوكيل الذكي، قم بصياغة سكريبت Google Apps Script مخصص لنظام الإشعارات بهذه المتطلبات الجديدة:

إرسال الإشعارات عبر Google Chat App Bot

لا تستخدم Webhook عادي، بل استخدم Google Chat App مهيّأ كبوت (Chat App Bot) ليتم إرسال الرسائل وتحديثها لاحقًا.

استخدم threadKey مساويًا لـ«كود الوحدة» ليخلق لكل عقار موضوعًا (thread) واحدًا في الغرفة. أي رسالة لاحقة بنفس الـthreadKey تصبح تحديثًا للرسالة الأصلية نفسها.

تخزين وتتبع الرسائل

أنشئ مستند Google Doc باسم AnyNameNotificationsLog (يمكنك تغييره لاحقًا).

عند إرسال أول إشعار لأي عقار (كدّ الوحدة)، خزّن في المستند صفًا جديدًا يحتوي على:

كود الوحدة

رسالة الـchatId أو messageId التي أرسلها البوت

عند كل تحديث لاحق لحالة العقار (مثلاً من “تخزين” إلى “تفكيك” أو إلى “نجاح نهائي”)، ابحث في المستند عن صف كود الوحدة، وبدل الرسالة باستخدام messageId نفسه عبر API التحديث (update).

منطق إطلاق الإشعارات وتحديثها

أول إشعار: عند توليد الكود لأول مرة (Script 1 – onFormSubmit)، أرسل رسالة بوت جديدة تحتوي على:

عنوان العقار (الكود + العنوان)

الحالة الحالية (“تخزين”)

رابط إلى الـGoogle Sheet أو تفاصيل مختصرة

تحديث الإشعار: في كل مرة يتغير فيها حقل “حالات النجاح” أو يتم الانتهاء من المعالجة الآلية المحليّة/AI (Scripts 2 & 3)، نفّذ تحديثًا واحدًا للرسالة السابقة في نفس الـthread عن طريق messageId المسجل في مستند AnyNameNotificationsLog، مع تغيير نص الحالة وإضافة ملخص جديد.

المتغيرات والإعدادات

احتفظ بالإعدادات التالية في Script Properties:

cpp
نسخ الكود
GOOGLE_FORM_ID
SHEET_ID
CHAT_APP_ID          // معرف Google Chat App
CHAT_SPACE          // الـspace أو room ID
NOTIFICATIONS_LOG_DOC_ID
استخدم OAuth المصادق عليه للبوت لإرسال وإدارة الرسائل عبر Chat REST API.

المخرجات المطلوبة

سكربت Google Apps Script موحد أو مقسّم على ثلاثة ملفات، يشتمل على:

onFormSubmit(e) لإنشاء الكود والإشعار الأول.

aiProcessor() و fallbackProcessor() لتحديث الإشعارات بناءً على تقدم المعالجة.

دوال مساعدة: sendOrUpdateNotification(code, status, details) التي تتحقق أولاً من وجود سجل في مستند AnyNameNotificationsLog ثم تنشئ أو تحدّث الرسالة المناسبة.
========================================
تصنيف البانات العقاريه

تصنيف العقارات وآلية التعامل معها
1. أنواع العقارات وأصنافها
يصنف النظام العقارات بناءً على حالة المعالجة ونوع العقار بعد المعالجة الناجحة.

أ. الحالة العامة للمعالجة (Processing State)
هذه هي النتيجة الأولية التي تصف ما إذا كانت الرسالة قد تمت معالجتها بنجاح أم لا.

✅ عقار ناجح:

التعريف: الرسالة تم تمريرها على جميع خطوات النظام (من الذكاء الاصطناعي وحتى التخزين في Notion وZoho) بنجاح كامل.

التعامل:

تُعدّل رسالة العقار في اشعار الخاص بالعقار ( الثريد ) في جوجل شات إلى التنسيق النهائي لـ "عقار ناجح".

يُخزّن في Notion google spreadsheets الرئيسيه و Zoho CRM  بحالة "عقار ناجح" بالإضافة إلى أي تصنيفات فرعية أخرى.

يتم تعديل الاشعار ليكون تفصبلي شمل (البيان , اسم المالك , رقم المالك , حاله الصور , اتاحه الوحده , كود الوحده , رابط صفحه نوشن ,رابط صفحه زوهو , رايط صفحه جوجول شيت , رقم صف العقار ,  حاله النجاح )  

❌ عقار فاشل:

التعريف: الرسالة لم تبدأ المعالجة أصلًا، أو توقفت بسبب خطأ في أي خطوة من الخطوات (مثل فشل تحليل الذكاء الاصطناعي، أو مشكلة في التخزين).

يتم ارسال الاشعار يشمل بيان الصفحه الخام
وقت الفشل
سبب الفشل ان وجد
مرفق معه قيمه حقل حالات النجاح 
التي توضح المراحل التي انجزها العقار بنجاح ليتم استكمال المعالجه من بعدها


لا تُرسل إشعارات متكررة للعقار الفاشل بل يتم تعديل العقار بتاريخ الفشل الجديد او تعديله بمحتوي العقار الناجح في حاله النجاح ).

يُمكن للنظام إعادة محاولة معالجته لاحقًا، مع تخطي الخطوات التي نجحت مسبقًا.

ب. أنواع العقار الناجح (Success Types)
هذه تصنيفات فرعية للعقارات التي نجحت معالجتها بشكل عام، وتساعد في فهم طبيعة العقار وعلاقته بالبيانات الأخرى.

🏢 عقار متعدد (Multi-Property Owner):

التعريف: العقار له نفس رقم المالك الموجود في عقارات أخرى سبق تخزينها في النظام.

التعامل:

يتم ربط هذا العقار بالعقارات الأخرى التابعة لنفس المالك في Notion و Zoho CRM (علاقة "مالك مشترك").

يُصنّف في حقل Status بـ  نوشن و جوجول شيت على أنه "عقار ناجح" و "عقار متعدد" معًا.

يعدل إشعارالعقار للمشرفين ليوضح أن المالك يمتلك أكثر من عقار.

🔁 عقار مكرر (Duplicate Property):

التعريف: العقار يتطابق تمامًا مع عقار آخر موجود في النظام من حيث: رقم المالك، المنطقة، المساحة، الدور، نوع العقار، وحالة العقار.

التعامل:

يُصنّف في حقل Status بـ  نوشن و جوجول شيت على أنه "عقار ناجح" و "عقار مكرر معًا.


يُرسل إشعار خاص للمراجعة اليدوية؛ حيث لا يحدد النظام تلقائيًا ما إذا كان التكرار حقيقيًا أو خطأ إدخال.

يمكن تحويله يدويًا إلى "عقار متعدد" أو استبعاده بعد المراجعة.

🗑️ عقار تالف / خطأ (Damaged Property):

التعريف: عقار تنجح معالجته بشكل عام، لكن توجد حقول أساسية إلزامية ناقصة فيه (مثل المنطقة، نوع الوحدة، حالة الوحدة) وقد تم استبدالها بقيمة "غير محدد" بواسطة النظام.

التعامل:

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "تالف / خطأ" معًا.

يُعتبر جزءًا من العقارات الناجحة، لكنه يدخل ضمن الملفات التي تُرسل يدويًا للمراجعة بسبب نقص بياناته.

يُرسل إشعار يوضح المشكلة.

✅ عقار ناجح عادي (Regular Success):

التعريف: هو أي عقار ناجح لا يندرج تحت تصنيف "متعدد"، "مكرر"، أو "تالف".

التعامل: يتم معالجته وتخزينه كعقار ناجح، مع إشعار عادي.

2. تسلسل التصنيف النهائي
عند انتهاء معالجة أي رسالة، يقوم النظام بتطبيق التسلسل المنطقي التالي لتحديد التصنيف النهائي للعقار في جوجل شيت

هل فشلت المعالجة بالكامل أو جزئيًا؟

إذا نعم → عقار فاشل.

هل المعالجة نجحت بالكامل؟

إذا نعم → عقار ناجح.

ثم يُكمل النظام التحقق من التصنيفات الفرعية:

هل يوجد حقول إلزامية ناقصة وتم تعويضها؟

إذا نعم → عقار تالف.

هل يتطابق مع مالك آخر؟

إذا نعم → عقار متعدد.

هل يتطابق تمامًا مع عقار آخر (معايير التكرار)؟

إذا نعم → عقار مكرر.

إذا لم ينطبق عليه أي من التصنيفات الفرعية السابقة؟

→ عقار عادي ناجح.

3. كيفية التعامل مع التصنيفات في النظام
 على قناة  الخاصة بشات جوجل


ب. داخل جوجل شيت - حقل Status
يُخزّن التصنيف النهائي والفرعي للعقار في حقل Status من نوع Multi-select، مما يسمح بحمل أكثر من قيمة في نفس الوقت (مثل: ["عقار ناجح", "عقار متعدد"]).

القيم المحتملة: عقار ناجح، عقار فاشل، عقار مكرر، عقار متعدد، تالف / خطأ.

ج. آلية "تاريخ آخر تحديث" (LastProcessedDate)
الغرض: ضمان عدم إعادة معالجة الرسائل القديمة التي نجحت بالفعل وتحسين أداء النظام.

الآلية:

النظام لا يُحدّث قيمة "تاريخ آخر تحديث" إلا إذا تأكد أن جميع الرسائل التي تم استقبالها في الشيت قبل هذا التاريخ، قد تم تصنيفها كـ #عقار_ناجح.

عند بدء التشغيل، يبدأ النظام بالبحث عن ومعالجة الرسائل الجديدة أو الفاشلة التي أتت بعد تاريخ آخر تحديث المسجل.

يُخزّن هذا التاريخ في Google Drive Sheet.

د. التعامل مع المشباكات (البيانات المتشابكة)
المشباكات هي رسائل تحمل تشابهات قوية مع رسائل أخرى.

عقار متعدد: يُعالج تلقائيًا ويُخزن عاديًا، مع إشعار للمشرفين.

عقار مكرر: يُخزن كـ "عقار ناجح – مكرر" ويُرسل إشعار "للمراجعة اليدوية".

عقار تالف: يُخزن كـ "عقار ناجح – تالف" ويُرسل إشعار بالمشكلة.

التصفية لمنع التكرار: قبل معالجة أي رسالة، يتم فحص قيمه حقل Status لمعرفه حاله النجاح  في وتاريخ آخر معالجة لمنع تكرار التحليل غير الضروري.

هـ. إشعارات العقارات المعالجة بنجاح
تُرسل إشعارات تفصيلية للمشرفين عند نجاح معالجة العقار (سواء كان عاديًا، متعددًا، مكررًا، أو تالفًا).

محتوى الإشعار:  
 (البيان , اسم المالك , رقم المالك , حاله الصور , اتاحه الوحده , كود الوحده , رابط صفحه نوشن ,رابط صفحه زوهو , رايط صفحه جوجول شيت , رقم صف العقار ,  حاله النجاح )  


الشرط: تُرسل الروابط فقط إذا نجحت جميع خطوات الإنشاء في Zoho و Notion.



ز. التعامل مع الفشل الكامل في تحليل الذكاء الاصطناعي
إذا فشلت جميع منصات الذكاء الاصطناعي في تحليل العقار، يُرسل النظام إشعارًا ذكيًا لجروب الإشراف:

🚫 تعذر تحليل هذا العقار باستخدام أي منصة ذكاء اصطناعي. هل ترغب في تحويل العقار إلى معالجة تلقائية؟ أم ستعالجه يدويًا بنفسك؟

يُمكن للمشرف الرد بـ "نعم" (لإعادة المحاولة تلقائيًا) أو "لا" (لتجاهل التحليل الملقائي).

ح. الصيغة المقترحة للكتابة اليدوية (Prompt Manual Format)
لإدخال البيانات يدويًا، يتم استخدام صيغة "Key: Value" بسيطة، حيث تكون كل معلومة في سطر منفصل (مثال: المنطقة: أحياء التجمع).

البرنامج يستخرج هذه القيم ويحولها إلى JSON، ويطبق عليها قواعد التحقق.

أي حقل غير موجود في الإدخال اليدوي يتم تعويضه بقيمة افتراضية (مثال: "غير محدد").














عايز اعدل البيان ده بالتحديثات دي

الإشعارات هتيجي على Google Chat App.



هيتم بناء Google Chat App على هيئة بوت باستخدام Google Apps Script.



البوت هو المسئول عن استقبال الإشعارات من النظام وإرسالها في الغرفة أو الشات المخصص.







---



٢. تدفق البيانات (المرحلة الأولى من المشروع)



1. استقبال البيان الأولي:



النظام يستقبل بيان العقار (مفصل أو جزئي).



البيان بيتخزن في Google Sheet كقاعدة بيانات أولية.



كل بيان جديد بيفتح Row جديد في Google Sheet.







2. توليد كود الوحدة الفريد (Unique Unit Code):



أول ما العقار يتسجل في الرو → يتم توليد كود وحدة فريد.



الكود يفضل مرتبط بنفس العقار طول دورة حياته.







3. إطلاق الإشعار الأولي في Google Chat:



مع تسجيل العقار لأول مرة يتم إشعار نجاح في Google Chat.



هذا الإشعار بمثابة بداية الثريد للعقار.



أي تحديثات مستقبلية على العقار بتروح كردود في نفس الثريد.











---



٣. حقول البيانات وقواعد الإلزام



كل الحقول اختيارية ولها قيم افتراضية:



النصوص = غير محدد



الأرقام = 0





ثلاث حقول إلزامية من ناحية منطق النظام:



1. المنطقة





2. نوع الوحدة





3. حالة الوحدة







لو أي حقل من دول فاضي → يتسجل افتراضيًا بـ غير محدد ولا يوقف العملية.



العقار اللي حقوله الرئيسية كلها غير محدد يتسجل كـ عقار تالف لكن يفضل يكمل في دورة النظام بدون تعطيل.







---

=================================



تصنيف العقارات وآلية التعامل معها

1. أنواع العقارات وأصنافها

يصنف النظام العقارات بناءً على حالة المعالجة ونوع العقار بعد المعالجة الناجحة.

أ. الحالة العامة للمعالجة (Processing State)

هذه هي النتيجة الأولية التي تصف ما إذا كانت الرسالة قد تمت معالجتها بنجاح أم لا.



✅ عقار ناجح:

التعريف: الرسالة تم تمريرها على جميع خطوات النظام (من الذكاء الاصطناعي وحتى التخزين في Notion وZoho) بنجاح كامل.

التعامل:

تُعدّل رسالة العقار في قناة  جوجل شات إلى التنسيق النهائي لـ "عقار ناجح".

يُخزّن في Notion و Zoho CRM بحالة "عقار ناجح" بالإضافة إلى أي تصنيفات فرعية أخرى.

يتم إرسال إشعار تفصيلي للمشرفين يتضمن روابط Notion و Zoho.

تُستخدم لتحديث "تاريخ آخر تحديث" للنظام.

❌ عقار فاشل:

التعريف: الرسالة لم تبدأ المعالجة أصلًا، أو توقفت بسبب خطأ في أي خطوة من الخطوات (مثل فشل تحليل الذكاء الاصطناعي، أو مشكلة في التخزين).
ب. أنواع العقار الناجح (Success Types)

هذه تصنيفات فرعية للعقارات التي نجحت معالجتها بشكل عام، وتساعد في فهم طبيعة العقار وعلاقته بالبيانات الأخرى.



🏢 عقار متعدد (Multi-Property Owner):

التعريف: العقار له نفس رقم المالك الموجود في عقارات أخرى سبق تخزينها في النظام.

التعامل:

يتم ربط هذا العقار بالعقارات الأخرى التابعة لنفس المالك في Notion و Zoho CRM (علاقة "مالك مشترك").

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "عقار متعدد" معًا.

يُرسل إشعار للمشرفين يوضح أن المالك يمتلك أكثر من عقار.

🔁 عقار مكرر (Duplicate Property):

التعريف: العقار يتطابق تمامًا مع عقار آخر موجود في النظام من حيث: رقم المالك، المنطقة، المساحة، الدور، نوع العقار، وحالة العقار.

التعامل:

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "عقار مكرر" معًا.

يُرسل إشعار خاص للمراجعة اليدوية؛ حيث لا يحدد النظام تلقائيًا ما إذا كان التكرار حقيقيًا أو خطأ إدخال.

يمكن تحويله يدويًا إلى "عقار متعدد" أو استبعاده بعد المراجعة.

🗑️ عقار تالف / خطأ (Damaged Property):

التعريف: عقار تنجح معالجته بشكل عام، لكن توجد حقول أساسية إلزامية ناقصة فيه (مثل المنطقة، نوع الوحدة، حالة الوحدة) وقد تم استبدالها بقيمة "غير محدد" بواسطة النظام.

التعامل:

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "تالف / خطأ" معًا.

يُعتبر جزءًا من العقارات الناجحة، لكنه يدخل ضمن الملفات التي تُرسل يدويًا للمراجعة بسبب نقص بياناته.

يُرسل إشعار يوضح المشكلة.

✅ عقار ناجح عادي (Regular Success):

التعريف: هو أي عقار ناجح لا يندرج تحت تصنيف "متعدد"، "مكرر"، أو "تالف".

التعامل: يتم معالجته وتخزينه كعقار ناجح، مع إشعار عادي.

2. تسلسل التصنيف النهائي

عند انتهاء معالجة أي رسالة، يقوم النظام بتطبيق التسلسل المنطقي التالي لتحديد التصنيف النهائي للعقار في Notion:



هل فشلت المعالجة بالكامل أو جزئيًا؟

إذا نعم → عقار فاشل.

هل المعالجة نجحت بالكامل؟

إذا نعم → عقار ناجح.

ثم يُكمل النظام التحقق من التصنيفات الفرعية:

هل يوجد حقول إلزامية ناقصة وتم تعويضها؟

إذا نعم → عقار تالف.

هل يتطابق مع مالك آخر؟

إذا نعم → عقار متعدد.

هل يتطابق تمامًا مع عقار آخر (معايير التكرار)؟

إذا نعم → عقار مكرر.

إذا لم ينطبق عليه أي من التصنيفات الفرعية السابقة؟

→ عقار عادي ناجح.

3. كيفية التعامل مع التصنيفات في النظام

أ. على قناة جوجل شات الخاصة بالبوت

#عقار_ناجح: تُضاف هذه العلامة بعد تعديل الرسالة بالكامل لتنسيق النجاح، ولن تتم معالجتها مرة أخرى أبدًا.

#عقار_فاشل: تُضاف هذه العلامة في بداية الرسالة مع رموز توضح مراحل الفشل، وستُعاد محاولة معالجتها تلقائيًا في الجلسات اللاحقة حتى تنجح.

ب. داخل Notion - حقل Status

يُخزّن التصنيف النهائي والفرعي للعقار في حقل Status من نوع Multi-select، مما يسمح بحمل أكثر من قيمة في نفس الوقت (مثل: ["عقار ناجح", "عقار متعدد"]).

القيم المحتملة: عقار ناجح، عقار فاشل، عقار مكرر، عقار متعدد، تالف / خطأ.

ج. آلية "تاريخ آخر تحديث" (LastProcessedDate)

الغرض: ضمان عدم إعادة معالجة الرسائل القديمة التي نجحت بالفعل وتحسين أداء النظام.

الآلية:

النظام لا يُحدّث قيمة "تاريخ آخر تحديث" إلا إذا تأكد أن جميع الرسائل التي تم استقبالها في القناة قبل هذا التاريخ، قد تم تصنيفها كـ #عقار_ناجح.

عند بدء التشغيل، يبدأ النظام بالبحث عن ومعالجة الرسائل الجديدة أو الفاشلة التي أتت بعد تاريخ آخر تحديث المسجل.

يُخزّن هذا التاريخ في Google Drive Sheet.

د. التعامل مع المشباكات (البيانات المتشابكة)

المشباكات هي رسائل تحمل تشابهات قوية مع رسائل أخرى.

عقار متعدد: يُعالج تلقائيًا ويُخزن عاديًا، مع إشعار للمشرفين.

عقار مكرر: يُخزن كـ "عقار ناجح – مكرر" ويُرسل إشعار "للمراجعة اليدوية".

عقار تالف: يُخزن كـ "عقار ناجح – تالف" ويُرسل إشعار بالمشكلة.

التصفية لمنع التكرار: قبل معالجة أي رسالة، يتم فحص وسمها في Telegram وتاريخ آخر معالجة لمنع تكرار التحليل غير الضروري.

هـ. إشعارات العقارات المعالجة بنجاح

تُرسل إشعارات تفصيلية للمشرفين عند نجاح معالجة العقار (سواء كان عاديًا، متعددًا، مكررًا، أو تالفًا).

محتوى الإشعار: وصف مختصر للحالة، كود الوحدة، اسم ورقم المالك، روابط صفحات العقار والمالك في Notion، ورابط سجل العقار في Zoho CRM.

الشرط: تُرسل الروابط فقط إذا نجحت جميع خطوات الإنشاء في Zoho و Notion.

و. نظام منع تكرار إشعارات العقارات الفاشلة

لتقليل الإشعارات المزعجة، لا يرسل النظام إشعارًا جديدًا للعقار الفاشل إلا إذا كان أول فشل له أو اختلفت خطوات الفشل عن المرة السابقة.

يتم ذلك عن طريق تخزين رمز حالة الفشل (مثل [ZN]) داخل رسالة Telegram نفسها، حيث يشير Z إلى رقم يرمز لحالة الفشل الشاملة وN إلى رمز الخطوات التي تمت بنجاح (مثل A للتحليل، B لـ Zoho).

يقوم النظام بمقارنة هذا الرمز قبل إرسال إشعار جديد.

ز. التعامل مع الفشل الكامل في تحليل الذكاء الاصطناعي

إذا فشلت جميع منصات الذكاء الاصطناعي في تحليل العقار، يُرسل النظام إشعارًا ذكيًا لجروب الإشراف:

🚫 تعذر تحليل هذا العقار باستخدام أي منصة ذكاء اصطناعي. هل ترغب في تحويل العقار إلى معالجة تلقائية؟ أم ستعالجه يدويًا بنفسك؟

يُمكن للمشرف الرد بـ "نعم" (لإعادة المحاولة تلقائيًا) أو "لا" (لتجاهل التحليل الملقائي).

ح. الصيغة المقترحة للكتابة اليدوية (Prompt Manual Format)

لإدخال البيانات يدويًا، يتم استخدام صيغة "Key: Value" بسيطة، حيث تكون كل معلومة في سطر منفصل (مثال: المنطقة: أحياء التجمع).

البرنامج يستخرج هذه القيم ويحولها إلى JSON، ويطبق عليها قواعد التحقق.

أي حقل غير موجود في الإدخال اليدوي يتم تعويضه بقيمة افتراضية (مثال: "غير محدد").

4. كود العقار التسلسلي (Serial Property Code)

يتم توليد كود العقار التسلسلي بواسطة النظام ويتم تحديث تنسيقه ليصبح:000-{نوع الحالة رقمياً}-{رقم المنطقة}-{تاريخ اليوم}-{رقم تسلسلي}

مثال: إذا كانت الحالة "تمليك" (رقم 3)، المنطقة "z5" (تصبح 5)، التاريخ "250725"، والرقم "1"، يكون الناتج النهائي: 000-3-5-250725-1.

يتم إزالة جميع الحروف اللاتينية (c, z, t, n) من الكود، واستبدال الحروف والفواصل بشرطة (-) فقط، وحذف كلمة "وحدة" بالكامل.

5. الحقول المطلوبة وتحليلها

الحقلشرط الوجودالقيم الممكنة أو طريقة الاستخراجالمنطقةإجباريبناءً على كلمات دالة أو مناطق معرفة مسبقًا (z1–z5)كود الوحدةإجبارييُولّد بناءً على نوع الحالة، المنطقة، التاريخ، والرقم التسلسلينوع الوحدةإجباريمن القيم: شقة، فيلا، دوبلكس، بنتهاوسحالة الوحدةإجباريمن القيم: فاضي، مفروش، تمليكالمساحةاختياريرقم فقط، القيمة الافتراضية: 00الدوراختياريمثل: "دور تاني"، القيمة الافتراضية: "غير محدد"السعراختياريرقم فقط، القيمة الافتراضية: 00المميزاتاختياريمن قائمة معينة فقط (يفضل تحويلها إلى قائمة/مصفوفة)العنواناختيارينص مستخرج من البيان، القيمة الافتراضية: "غير محدد"اسم الموظفاختيارييتم استخراجه بعد كلمة "تبع"، من قائمة أسماء محددةاسم المالكاختيارياسم شخص، إن لم يوجد: "غير محدد"رقم المالكاختياري11 رقمًا، إن لم يوجد: 01000000000اتاحة العقاراختياريمتاح، غير متاح، مؤجر، القيمة الافتراضية: "غير محدد"حالة الصوراختياريبصور، بدون صور، صور غير محددة، القيمة الافتراضية: "صور غير محددة"تفاصيل كاملةإجباري دائمًاالنسخة الأصلية من البيان بعد حذف اسم المالك ورقمه، واتاحة العقار، وحالة الصور

التصدير إلى "جداول بيانات Google"

6. التعامل مع الإدخالات المتعددة

إذا احتوت الرسالة الواحدة على أكثر من بيان عقاري، يتم فصل كل بيان ومعالجته ككيان منفصل.

كل بيان منفصل يحصل على كود وحدة مستقل برقمه التسلسلي الخاص به.




عرض طريقة التفكير
بالتأكيد، سأقوم بتحديث البيان الخاص بتصنيف العقارات وآلية التعامل معها ليتضمن التغييرات التي طلبتها بخصوص تطبيق Google Chat وGoogle Sheet.

تصنيف العقارات وآلية التعامل معها (تحديث)
1. تدفق البيانات (المرحلة الأولى من المشروع)
استقبال البيان الأولي وتخزينه:

يستقبل النظام بيان العقار (مفصل أو جزئي).

يُخزّن البيان مباشرة في Google Sheet كقاعدة بيانات أولية.

كل بيان جديد يُنشئ صفًا (Row) جديدًا في Google Sheet.

توليد كود الوحدة الفريد (Unique Unit Code):

بمجرد تسجيل العقار في الصف بـ Google Sheet، يتم توليد كود وحدة فريد.

يظل هذا الكود مرتبطًا بنفس العقار طوال دورة حياته.

إطلاق الإشعار الأولي في Google Chat:

مع تسجيل العقار لأول مرة في Google Sheet، يتم إرسال إشعار نجاح أولي في Google Chat App.

هذا الإشعار بمثابة بداية الثريد (Thread) الخاص بهذا العقار في Google Chat.

أي تحديثات مستقبلية على العقار (مثل تغيير حالته أو إتمام خطوات المعالجة) ستُرسل كـ ردود في نفس الثريد في Google Chat.

2. حقول البيانات وقواعد الإلزام
جميع الحقول اختيارية ولها قيم افتراضية:

النصوص: القيمة الافتراضية هي "غير محدد".

الأرقام: القيمة الافتراضية هي 0.

ثلاثة حقول إلزامية من ناحية منطق النظام:

المنطقة

نوع الوحدة

حالة الوحدة

التعامل مع الحقول الفارغة:

إذا كان أي من هذه الحقول الثلاثة (المنطقة، نوع الوحدة، حالة الوحدة) فارغًا، يتم تسجيله افتراضيًا بـ "غير محدد" ولا يوقف عملية المعالجة.

العقار الذي تكون جميع حقوله الرئيسية (المنطقة، نوع الوحدة، حالة الوحدة) مسجلة بـ "غير محدد" يُصنّف كـ عقار تالف، لكنه يستمر في دورة النظام دون تعطيل.

3. تصنيف العقارات وآلية التعامل معها
يصنف النظام العقارات بناءً على حالة المعالجة ونوع العقار بعد المعالجة الناجحة.

أ. الحالة العامة للمعالجة (Processing State)
هذه هي النتيجة الأولية التي تصف ما إذا كانت الرسالة قد تمت معالجتها بنجاح أم لا.

✅ عقار ناجح:

التعريف: الرسالة تم تمريرها على جميع خطوات النظام (من تحليل الذكاء الاصطناعي وحتى التخزين في Notion وZoho) بنجاح كامل.

التعامل:

تُعدّل رسالة العقار في Google Sheet لتعكس حالة النجاح.

يُخزّن في Notion و Zoho CRM بحالة "عقار ناجح" بالإضافة إلى أي تصنيفات فرعية أخرى.

يتم إرسال إشعار تفصيلي للمشرفين في Google Chat App (كرّد في ثريد العقار) يتضمن روابط Notion و Zoho.

تُستخدم لتحديث "تاريخ آخر تحديث" للنظام.

❌ عقار فاشل:

التعريف: الرسالة لم تبدأ المعالجة أصلًا، أو توقفت بسبب خطأ في أي خطوة من الخطوات (مثل فشل تحليل الذكاء الاصطناعي، أو مشكلة في التخزين).

التعامل:

لا يتغير نص البيان الأصلي في Google Sheet، ولكن تُضاف علامة ❌ في الحقل المخصص للحالة مع رموز توضح خطوات الفشل والنجاح الجزئي (مثلاً: ❌ عقار فاشل | ✔️ تحليل، ✖️ Zoho).

يُخزن في Notion بحالة "عقار فاشل".

لا تُرسل إشعارات متكررة في Google Chat للعقار الفاشل إلا إذا تغيرت حالة الفشل (مثل تحسن جزئي في المعالجة).

يُمكن للنظام إعادة محاولة معالجته لاحقًا، مع تخطي الخطوات التي نجحت مسبقًا.

ب. أنواع العقار الناجح (Success Types)
هذه تصنيفات فرعية للعقارات التي نجحت معالجتها بشكل عام، وتساعد في فهم طبيعة العقار وعلاقته بالبيانات الأخرى.

🏢 عقار متعدد (Multi-Property Owner):

التعريف: العقار له نفس رقم المالك الموجود في عقارات أخرى سبق تخزينها في النظام.

التعامل:

يتم ربط هذا العقار بالعقارات الأخرى التابعة لنفس المالك في Notion و Zoho CRM (علاقة "مالك مشترك").

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "عقار متعدد" معًا.

يُرسل إشعار للمشرفين في Google Chat يوضح أن المالك يمتلك أكثر من عقار.

🔁 عقار مكرر (Duplicate Property):

التعريف: العقار يتطابق تمامًا مع عقار آخر موجود في النظام من حيث: رقم المالك، المنطقة، المساحة، الدور، نوع العقار، وحالة العقار.

التعامل:

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "عقار مكرر" معًا.

يُرسل إشعار خاص للمراجعة اليدوية في Google Chat؛ حيث لا يحدد النظام تلقائيًا ما إذا كان التكرار حقيقيًا أو خطأ إدخال.

يمكن تحويله يدويًا إلى "عقار متعدد" أو استبعاده بعد المراجعة.

🗑️ عقار تالف / خطأ (Damaged Property):

التعريف: عقار تنجح معالجته بشكل عام، لكن توجد حقول أساسية إلزامية (منطقياً) ناقصة فيه (مثل المنطقة، نوع الوحدة، حالة الوحدة) وقد تم استبدالها بقيمة "غير محدد" بواسطة النظام.

التعامل:

يُصنّف في حقل Status بـ Notion على أنه "عقار ناجح" و "تالف / خطأ" معًا.

يُعتبر جزءًا من العقارات الناجحة، لكنه يدخل ضمن الملفات التي تُرسل يدويًا للمراجعة بسبب نقص بياناته.

يُرسل إشعار في Google Chat يوضح المشكلة.

✅ عقار ناجح عادي (Regular Success):

التعريف: هو أي عقار ناجح لا يندرج تحت تصنيف "متعدد"، "مكرر"، أو "تالف".

التعامل: يتم معالجته وتخزينه كعقار ناجح، مع إشعار عادي في Google Chat.

4. تسلسل التصنيف النهائي
عند انتهاء معالجة أي رسالة، يقوم النظام بتطبيق التسلسل المنطقي التالي لتحديد التصنيف النهائي للعقار في Notion:

هل فشلت المعالجة بالكامل أو جزئيًا؟

إذا نعم → عقار فاشل.

هل المعالجة نجحت بالكامل؟

إذا نعم → عقار ناجح.

ثم يُكمل النظام التحقق من التصنيفات الفرعية:

هل يوجد حقول إلزامية (منطقياً) ناقصة وتم تعويضها؟

إذا نعم → عقار تالف.

هل يتطابق مع مالك آخر؟

إذا نعم → عقار متعدد.

هل يتطابق تمامًا مع عقار آخر (معايير التكرار)؟

إذا نعم → عقار مكرر.

إذا لم ينطبق عليه أي من التصنيفات الفرعية السابقة؟

→ عقار عادي ناجح.

5. كيفية التعامل مع التصنيفات في النظام
أ. على Google Sheet (قاعدة البيانات الأولية)
يتم تحديث حقل الحالة في الصف الخاص بالعقار ليعكس ✅ عقار ناجح أو ❌ عقار فاشل مع رموز الخطوات.

ب. داخل Notion - حقل Status
يُخزّن التصنيف النهائي والفرعي للعقار في حقل Status من نوع Multi-select، مما يسمح بحمل أكثر من قيمة في نفس الوقت (مثل: ["عقار ناجح", "عقار متعدد"]).

القيم المحتملة: عقار ناجح، عقار فاشل، عقار مكرر، عقار متعدد، تالف / خطأ.

ج. آلية "تاريخ آخر تحديث" (LastProcessedDate)
الغرض: ضمان عدم إعادة معالجة الرسائل القديمة التي نجحت بالفعل وتحسين أداء النظام.

الآلية:

النظام لا يُحدّث قيمة "تاريخ آخر تحديث" إلا إذا تأكد أن جميع البيانات التي تم استقبالها في Google Sheet قبل هذا التاريخ، قد تم تصنيفها كـ ✅ عقار ناجح.

عند بدء التشغيل، يبدأ النظام بالبحث عن ومعالجة البيانات الجديدة أو الفاشلة التي أتت بعد تاريخ آخر تحديث المسجل.
يتم تخزين مراحل الاشعارات في شيت الاشعارات

د. التعامل مع المشباكات (البيانات المتشابكة)
المشباكات هي بيانات تحمل تشابهات قوية مع بيانات أخرى.

عقار متعدد: يُعالج تلقائيًا ويُخزن عاديًا، مع إشعار للمشرفين في Google Chat.

عقار مكرر: يُخزن كـ "عقار ناجح – مكرر" ويُرسل إشعار "للمراجعة اليدوية" في Google Chat.

عقار تالف: يُخزن كـ "عقار ناجح – تالف" ويُرسل إشعار بالمشكلة في Google Chat.

التصفية لمنع التكرار: قبل معالجة أي بيان، يتم فحص حالته في Google Sheet وتاريخ آخر معالجة لمنع تكرار التحليل غير الضروري.

هـ. إشعارات العقارات المعالجة بنجاح (عبر Google Chat App)
تُرسل إشعارات تفصيلية للمشرفين في Google Chat App عند نجاح معالجة العقار (سواء كان عاديًا، متعددًا، مكررًا، أو تالفًا).

محتوى الإشعار: وصف مختصر للحالة، كود الوحدة، اسم ورقم المالك، روابط صفحات العقار والمالك في Notion، ورابط سجل العقار في Zoho CRM.

الشرط: تُرسل الروابط فقط إذا نجحت جميع خطوات الإنشاء في Zoho و Notion.

و. نظام منع تكرار إشعارات العقارات الفاشلة (عبر Google Chat App)
لتقليل الإشعارات المزعجة، لا يرسل النظام إشعارًا جديدًا في Google Chat App للعقار الفاشل إلا إذا كان أول فشل له أو اختلفت خطوات الفشل عن المرة السابقة.

يتم ذلك عن طريق تخزين رمز حالة الفشل (مثل [ZN]) داخل سجل العقار في Google Sheet، حيث يشير Z إلى رقم يرمز لحالة الفشل الشاملة وN إلى رمز الخطوات التي تمت بنجاح (مثل A للتحليل، B لـ Zoho).

يقوم النظام بمقارنة هذا الرمز قبل إرسال إشعار جديد.

ز. التعامل مع الفشل الكامل في تحليل الذكاء الاصطناعي (عبر Google Chat App)
إذا فشلت جميع منصات الذكاء الاصطناعي في تحليل العقار، يُرسل النظام إشعارًا ذكيًا في Google Chat App لغرفة الإشراف:

🚫 تعذر تحليل هذا العقار باستخدام أي منصة ذكاء اصطناعي. هل ترغب في تحويل العقار إلى معالجة تلقائية؟ أم ستعالجه يدويًا بنفسك؟

يُمكن للمشرف الرد بـ "نعم" (لإعادة المحاولة تلقائيًا) أو "لا" (لتجاهل التحليل التلقائي).


إذا احتوت الرسالة الواحدة على أكثر من بيان عقاري، يتم فصل كل بيان ومعالجته ككيان منفصل.

كل بيان منفصل يحصل على كود وحدة مستقل برقمه التسلسلي الخاص به.
=============================================
مرحباً أيها الوكيل الذكي!

مهمتك هي بناء نظام إشعارات قوي وذكي باستخدام Google Apps Script. هذا النظام سيكون مسؤولاً عن إرسال وتحديث الإشعارات المتعلقة ببيانات العقارات، وسيكون مصمماً بشكل يسمح لأجزاء أخرى من المشروع باستدعاء وظائفه بسهولة.

1. الهدف الرئيسي للنظام
الهدف الأساسي هو إنشاء سكريبت في Google Apps Script يتعامل مع بيانات العقارات من Google Sheet، ويرسل إشعارات فورية وتحديثات إلى Google Chat App Bot، مع الاحتفاظ بسجل لكل الإشعارات المرسلة.

2. المكونات الأساسية للنظام
نظام الإشعارات سيعتمد على ثلاثة مكونات رئيسية:

بوت تطبيق Google Chat:

هذا البوت سيكون القناة الأساسية لإرسال الإشعارات.

مهم جداً: عندما ترسل إشعاراً عن عقار معين، استخدم "كود الوحدة" الخاص به كمفتاح للمحادثة (threadKey). هذا يعني أن كل التحديثات اللاحقة لنفس العقار ستظهر كردود في نفس المحادثة الأصلية، مما يحافظ على تنظيم الإشعارات.

جدول بيانات Google Sheet لسجل الإشعارات (notification log):

اسمه "notification log" وموجود في مجلد "Elsweedy real estate data".

سيكون هذا الجدول بمثابة "دفتر يومية" لكل الإشعارات التي يرسلها البوت.

عند إرسال أول إشعار لعقار (باستخدام كود الوحدة الخاص به)، يجب أن تسجل في هذا الجدول: "كود الوحدة"، "معرف الرسالة" الذي يرجعه Google Chat (messageId)، "تاريخ آخر تحديث"، و"الحالة" الحالية.

عندما تحتاج لتحديث إشعار موجود، ابحث عن كود الوحدة في هذا السجل، واستخدم "معرف الرسالة" المسجل لتحديث الرسالة الأصلية في Google Chat.

جدول بيانات Google Sheet الرئيسي (Elsweedy real estate sheet):

اسمه "Elsweedy real estate sheet" وورقة العمل المرتبطة بالنموذج اسمها "Aqar_bot".

هذا هو المصدر الرئيسي لبيانات العقارات التي سيقرأها السكريبت ويرسل إشعارات عنها.

3. منطق إرسال وتحديث الإشعارات
يجب أن يتعامل النظام مع نوعين من الإشعارات:

الإشعار الأولي (عند استقبال بيان جديد):

يتم إطلاق هذا الإشعار عندما يتم إرسال نموذج Google Form جديد (عبر دالة onFormSubmit).

محتوى الإشعار الأولي يجب أن يتضمن: عنوان العقار (كود الوحدة + العنوان)، الحالة الحالية ("تخزين")، ورابط لصف العقار في Google Sheet أو تفاصيل مختصرة.

تحديث الإشعارات (عند تقدم المعالجة أو حدوث فشل):

عندما تتغير حالة العقار في جدول البيانات الرئيسي (مثلاً، بعد التحليل بالذكاء الاصطناعي أو التخزين في Notion/Zoho)، يجب على النظام تحديث الإشعار السابق في نفس المحادثة على Google Chat.

محتوى إشعار النجاح: إذا تمت معالجة العقار بنجاح، يجب أن يكون الإشعار مفصلاً جداً، ويحتوي على:

علامة نجاح (✅ تم تخزين عقار جديد بنجاح).

البيان الكامل للعقار.

اسم المالك ورقمه.

حالة الصور وإتاحة الوحدة.

كود الوحدة.

روابط مهمة: رابط صفحة العقار في Google Sheet (مع رقم الصف)، وروابط صفحات العقار والمالك في Notion، ورابط سجل العقار في Zoho CRM.

ملاحظة: يجب إرسال روابط Notion و Zoho فقط إذا كانت هذه الخطوات قد تمت بنجاح.

محتوى إشعار الفشل: إذا فشلت المعالجة، يجب أن يوضح الإشعار:

علامة فشل (❌ عقار فاشل).

كود الوحدة.

وقت الفشل.

سبب الفشل (إذا توفر).

المراحل التي تم إنجازها بنجاح (مثلاً: ✔️ تحليل، ✖️ Zoho).

البيان الخام للعقار.

قاعدة مهمة لعدم تكرار إشعارات الفشل: لا ترسل إشعار فشل جديد إذا لم تتغير حالة الفشل (المراحل المنجزة). فقط قم بتحديث الرسالة الموجودة في المحادثة بنفس معرف الرسالة. إذا تحول العقار من فاشل إلى ناجح، قم بتحديث الرسالة لتصبح إشعار نجاح كامل.

4. الدوال الأساسية المطلوبة (الوظائف)
يجب أن يقوم وكيل الذكاء الاصطناعي بإنشاء الدوال التالية، مع التأكيد على أنها مستقلة وقابلة للاستدعاء من أي مكان في السكريبت:

onFormSubmit(e):

هذه هي نقطة البداية عند كل إرسال نموذج جديد.

تقوم بقراءة البيانات، وتعيين القيم الافتراضية، وتوليد كود الوحدة، وإرسال الإشعار الأولي.

يجب أن تستدعي الدوال الأخرى للقيام بمهامها.

sendOrUpdateNotification(unitCode, status, details, isInitial):

هذه هي الدالة الأساسية لإرسال وتحديث الرسائل في Google Chat.

تتحقق من سجل الإشعارات لمعرفة ما إذا كان هناك messageId موجود لهذا العقار.

إذا كان موجوداً (وليس إشعاراً أولياً)، تقوم بتحديث الرسالة الموجودة. وإلا، ترسل رسالة جديدة.

تستخدم unitCode كـ threadKey.

تقوم ببناء محتوى الرسالة بناءً على status و details (وتفاصيل العقار الكاملة للنجاح/الفشل).

تسجل/تحدث messageId و Last Updated و Status في سجل الإشعارات.

generateUnitCode(sheet, unitCodeColIndex):

تولّد كود وحدة فريد بالتنسيق DD.MM.YYYY-NNN (تاريخ اليوم - رقم تسلسلي يومي).

setDefaultValues(sheet, rowNum, headers):

تضع القيم الافتراضية للحقول الاختيارية (مثل "غير محدد" للنصوص و 0 للأرقام) في الصف الجديد.

splitMultiValueField(multiValueField):

تقوم بتقسيم النصوص التي تحتوي على بيانات عقارية متعددة إلى بيانات منفصلة، بناءً على الأسطر الفارغة أو الرموز كفواصل.

updatePropertyStatus(sheet, rowNum, colIndex, status):

تحدث حالة العقار في جدول البيانات الرئيسي، وتدعم الحقول متعددة الاختيارات.

checkAndMarkDamaged(sheet, rowNum, headers, unitCode, statusColIndex):

تتحقق مما إذا كانت الحقول الإلزامية منطقياً (المنطقة، نوع الوحدة، حالة الوحدة) فارغة أو "غير محدد". إذا كانت كذلك، تصنف العقار كـ "تالف" وترسل إشعاراً بذلك.

processWithAI(unitCode, statement) و fallbackAnalysis(unitCode, statement):

هذه الدوال هي مجرد أماكن محجوزة في هذه المرحلة. يجب أن يتم إنشاؤها، ولكن تنفيذها سيكون بسيطاً حالياً. سيتم تفصيل منطق الاتصال بمنصات الذكاء الاصطناعي والتحليل المحلي فيها في المراحل القادمة.

5. الإعدادات والمتغيرات
يجب تخزين جميع الإعدادات المهمة (مثل معرفات الجداول، معرفات بوت Chat، ومفاتيح API) في خصائص السكريبت (Script Properties).

هذه الخصائص تشمل: GOOGLE_FORM_ID, SHEET_ID, CHAT_APP_ID, CHAT_SPACE, NOTIFICATIONS_LOG_SHEET_ID, بالإضافة إلى مفاتيح API لمنصات الذكاء الاصطناعي.

6. المصادقة (Authentication)
يجب أن يكون السكريبت مصرحاً له باستخدام OAuth للوصول إلى Google Sheets API و Google Chat API.

يجب أن يتم تشغيله كـ "أنا" (المطور) وأن يكون لديه الصلاحيات اللازمة للتفاعل مع بوت Google Chat.

7. إرشادات التطوير
النمطية: تأكد أن كل دالة مستقلة ويمكن استدعاؤها بسهولة.

معالجة الأخطاء: استخدم try-catch لتجنب توقف السكريبت، وسجل الأخطاء باستخدام Logger.log.

التسجيل: استخدم Logger.log لتتبع سير عمل السكريبت.

التفاعل مع الـ APIs: استخدم UrlFetchApp لإجراء مكالمات API الخارجية (مثل Google Chat API) مع تنسيق صحيح للرؤوس والبيانات.

========================================
صفحه تخزين البروميت المستخدم في التحليل بواسطه منصات الذكاء الاصطانعي او بواسطه google app script عن طريق كود java script
بيانات الصفحه 
اسم الملف : Flexible Real Estate Analysis Prompt
اسم المجلد : Elsweedy real estate data
رابط الصفحه : https://docs.google.com/document/d/14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk/edit?tab=t.0
id : 14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk
اسم الصفحه : موجه التحليل العقاري المرن :

=========================================

طريقه تنفيذ المرحله

---

أفضل تصميم عملي:

1. سكربت 1: استقبال البيانات (Input Handler)

يستقبل البيانات (جوجول فورم :  ضع بيان عقارك هنا )

يخزنها في Google Sheet : Elsweedy real estate sheet

يعدل حقل حالات النجاح : يضيف قيمه:  تخزين
ويرسل اشعارا بناء عل ذالك 

في حاله الفشل يتم تعديل قيمه حقل Status : عقار فاشل
ويرسل اشعارا بتاءا علي ذالك مضافا اليه سبب الفشل

2. سكربت 2: التحليل بالذكاء الاصطناعي (AI Processor)
يقرأ السجلات الجاهزة للتحليل
يرسلها للذكاء الاصطناعي
يخزن النتيجة
في حاله النجاح يعدل حقل حالات النجاح : يضيف قيمه:  تفكيك
ويرسل اشعارا بناء عل ذالك 

في حاله الفشل يتم تعديل قيمه حقل Status : عقار فاشل
ويرسل اشعارا بتاءا علي ذالك مضافا اليه سبب الفشل



3. سكربت 3: التحليل المحلي (Fallback Processor)

يقرأ السجلات اللي فشلت

يقرأ البرومبت من Docs

يحلل بالنصوص وجافاسكريبت

يخزن النتيجة
في حاله النجاح يعدل حقل حالات النجاح : يضيف قيمه:  تفكيك
ويعدل قيمه حقل : Status  ويزيل قيمه عقار فاشل
ويرسل اشعارا بناء عل ذالك 

في حاله الفشل
يتم ارسال اشعار الي المستخدم






















