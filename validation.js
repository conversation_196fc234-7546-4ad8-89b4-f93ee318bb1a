/**
 * Validation and checking functions for property data
 */

// ===== DEFAULT VALUES =====
const DEFAULT_VALUES = {
  text: 'غير محدد',
  number: 0,
  date: null,
  phone: '01000000000'
};

// ===== REQUIRED FIELDS =====
const REQUIRED_FIELDS = ['المنطقة', 'نوع الوحدة', 'حالة الوحدة'];

// ===== VALIDATION FUNCTIONS =====

/**
 * Set default values for empty fields
 * @param {Sheet} sheet - Google Sheet object
 * @param {number} rowNum - Row number to process
 * @param {Object} headers - Column headers map
 */
function setDefaultValues(sheet, rowNum, headers) {
  const fieldTypes = getFieldTypes();
  
  Object.keys(headers).forEach(header => {
    const colIndex = headers[header];
    const value = sheet.getRange(rowNum, colIndex).getValue();
    
    if (!value || value === '') {
      const fieldType = fieldTypes[header] || 'text';
      const defaultValue = DEFAULT_VALUES[fieldType];
      
      if (defaultValue !== null) {
        sheet.getRange(rowNum, colIndex).setValue(defaultValue);
      }
    }
  });
  
  console.log(`Default values set for row ${rowNum}`);
}

/**
 * Check and mark damaged properties
 * @param {Sheet} sheet - Google Sheet object
 * @param {number} rowNum - Row number to check
 * @param {Object} headers - Column headers map
 * @param {string} unitCode - Unit code
 * @param {number} statusColIndex - Status column index
 */
function checkAndMarkDamaged(sheet, rowNum, headers, unitCode, statusColIndex) {
  let isDamaged = true;
  
  // Check required fields
  for (const field of REQUIRED_FIELDS) {
    if (headers[field]) {
      const value = sheet.getRange(rowNum, headers[field]).getValue();
      if (value && value !== 'غير محدد') {
        isDamaged = false;
        break;
      }
    }
  }
  
  if (isDamaged) {
    updatePropertyStatus(sheet, rowNum, statusColIndex, 'عقار تالف');
    
    // Send damage notification
    sendOrUpdateNotification(unitCode, 'تالف', {
      reason: 'Missing required fields',
      missingFields: REQUIRED_FIELDS,
      row: rowNum
    }, false);
    
    console.log(`Property ${unitCode} marked as damaged`);
  }
  
  return isDamaged;
}

/**
 * Update property status (supports multi-select)
 * @param {Sheet} sheet - Google Sheet object
 * @param {number} rowNum - Row number
 * @param {number} colIndex - Column index
 * @param {string} status - Status to add
 */
function updatePropertyStatus(sheet, rowNum, colIndex, status) {
  const currentValue = sheet.getRange(rowNum, colIndex).getValue() || '';
  const statuses = currentValue ? currentValue.split(',').map(s => s.trim()) : [];
  
  if (!statuses.includes(status)) {
    statuses.push(status);
    sheet.getRange(rowNum, colIndex).setValue(statuses.join(', '));
  }
}

/**
 * Remove property status
 * @param {Sheet} sheet - Google Sheet object
 * @param {number} rowNum - Row number
 * @param {number} colIndex - Column index
 * @param {string} status - Status to remove
 */
function removePropertyStatus(sheet, rowNum, colIndex, status) {
  const currentValue = sheet.getRange(rowNum, colIndex).getValue() || '';
  const statuses = currentValue.split(',').map(s => s.trim());
  
  const newStatuses = statuses.filter(s => s !== status);
  sheet.getRange(rowNum, colIndex).setValue(newStatuses.join(', '));
}

/**
 * Check for duplicate properties
 * @param {Sheet} sheet - Google Sheet object
 * @param {Object} propertyData - Property data to check
 * @param {Object} headers - Column headers map
 * @return {Object} Duplicate check result
 */
function checkDuplicateProperty(sheet, propertyData, headers) {
  const dataRange = sheet.getDataRange();
  const data = dataRange.getValues();
  
  const duplicateFields = ['رقم المالك', 'المنطقة', 'المساحة', 'الدور', 'نوع الوحدة', 'حالة الوحدة'];
  const multiOwnerField = 'رقم المالك';
  
  let isDuplicate = false;
  let isMultiOwner = false;
  let duplicateRows = [];
  
  for (let i = 1; i < data.length; i++) {
    let matchCount = 0;
    
    // Check multi-owner
    if (data[i][headers[multiOwnerField] - 1] === propertyData[multiOwnerField]) {
      isMultiOwner = true;
    }
    
    // Check duplicate
    for (const field of duplicateFields) {
      if (headers[field] && data[i][headers[field] - 1] === propertyData[field]) {
        matchCount++;
      }
    }
    
    if (matchCount === duplicateFields.length) {
      isDuplicate = true;
      duplicateRows.push(i + 1);
    }
  }
  
  return {
    isDuplicate,
    isMultiOwner,
    duplicateRows
  };
}

/**
 * Validate phone number
 * @param {string} phone - Phone number to validate
 * @return {boolean} Is valid
 */
function validatePhoneNumber(phone) {
  const phoneStr = String(phone).replace(/\D/g, '');
  return phoneStr.length === 11 && phoneStr.startsWith('01');
}

/**
 * Validate property data
 * @param {Object} data - Property data
 * @return {Object} Validation result
 */
function validatePropertyData(data) {
  const errors = [];
  
  // Validate phone
  if (data['رقم المالك'] && !validatePhoneNumber(data['رقم المالك'])) {
    errors.push('Invalid phone number');
  }
  
  // Validate area
  if (data['المساحة'] && isNaN(data['المساحة'])) {
    errors.push('Area must be a number');
  }
  
  // Validate price
  if (data['السعر'] && isNaN(data['السعر'])) {
    errors.push('Price must be a number');
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

/**
 * Get field types mapping
 * @return {Object} Field types
 */
function getFieldTypes() {
  return {
    'المنطقة': 'text',
    'حالة الصور': 'text',
    'Status': 'text',
    'اتاحة العقار': 'text',
    'حالة الوحدة': 'text',
    'اسم الموظف': 'text',
    'نوع الوحده': 'text',
    'الدور': 'text',
    'كود الوحدة': 'text',
    'المساحة': 'number',
    'السعر': 'number',
    'تفاصيل كاملة': 'text',
    'العنوان': 'text',
    'تاريخ آخر معالجة': 'date',
    'تاريخ الانشاء': 'date',
    'تاريخ التذكير': 'date',
    'اسم المالك': 'text',
    'رقم المالك': 'phone',
    'حالات النجاح': 'text'
  };
}

// ===== MOCK FUNCTIONS FOR LOCAL TESTING =====
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    setDefaultValues,
    checkAndMarkDamaged,
    updatePropertyStatus,
    removePropertyStatus,
    checkDuplicateProperty,
    validatePhoneNumber,
    validatePropertyData,
    getFieldTypes
  };
}