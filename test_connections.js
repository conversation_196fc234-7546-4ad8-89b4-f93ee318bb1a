/**
 * Test script to verify all Google Services connections
 * Run these functions to test each component of the Aqar Bot system
 */

// ===== MAIN TEST FUNCTION =====

/**
 * Run all tests and generate a comprehensive report
 */
function runAllTests() {
  console.log('🧪 Starting Aqar Bot System Tests...');
  console.log('=' .repeat(50));
  
  const results = {
    timestamp: new Date(),
    tests: [],
    summary: {
      total: 0,
      passed: 0,
      failed: 0,
      warnings: 0
    }
  };

  // Test 1: Script Properties
  results.tests.push(testScriptProperties());
  
  // Test 2: Google Sheets Access
  results.tests.push(testGoogleSheetsAccess());
  
  // Test 3: Google Forms Connection
  results.tests.push(testGoogleFormsConnection());
  
  // Test 4: Notification Log Sheet
  results.tests.push(testNotificationLogSheet());
  
  // Test 5: Prompt Document Access
  results.tests.push(testPromptDocumentAccess());
  
  // Test 6: AI API Keys
  results.tests.push(testAIAPIKeys());
  
  // Test 7: Service Account
  results.tests.push(testServiceAccount());
  
  // Calculate summary
  results.tests.forEach(test => {
    results.summary.total++;
    if (test.status === 'PASS') results.summary.passed++;
    else if (test.status === 'FAIL') results.summary.failed++;
    else if (test.status === 'WARNING') results.summary.warnings++;
  });

  // Generate report
  generateTestReport(results);
  
  return results;
}

// ===== INDIVIDUAL TEST FUNCTIONS =====

/**
 * Test Script Properties configuration
 */
function testScriptProperties() {
  const test = {
    name: 'Script Properties Configuration',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const properties = PropertiesService.getScriptProperties();
    const requiredProps = [
      'GOOGLE_FORM_ID',
      'SHEET_ID',
      'NOTIFICATIONS_LOG_SHEET_ID',
      'PROMPT_DOC_ID',
      'OPENAI_API_KEY',
      'SERVICE_ACCOUNT_KEY'
    ];

    requiredProps.forEach(prop => {
      const value = properties.getProperty(prop);
      if (!value) {
        test.status = 'FAIL';
        test.errors.push(`Missing property: ${prop}`);
      } else {
        test.details.push(`✅ ${prop}: Set`);
      }
    });

    if (test.errors.length === 0) {
      test.details.push(`All ${requiredProps.length} required properties are set`);
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error accessing properties: ${error.toString()}`);
  }

  return test;
}

/**
 * Test Google Sheets access
 */
function testGoogleSheetsAccess() {
  const test = {
    name: 'Google Sheets Access',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const sheetId = PropertiesService.getScriptProperties().getProperty('SHEET_ID');
    const spreadsheet = SpreadsheetApp.openById(sheetId);
    const sheet = spreadsheet.getSheetByName('Aqar_bot');

    if (!sheet) {
      test.status = 'FAIL';
      test.errors.push('Sheet "Aqar_bot" not found');
    } else {
      test.details.push(`✅ Main sheet accessible: ${spreadsheet.getName()}`);
      test.details.push(`✅ Aqar_bot sheet found with ${sheet.getLastRow()} rows`);
      
      // Test headers
      const headers = sheet.getRange(1, 1, 1, sheet.getLastColumn()).getValues()[0];
      test.details.push(`✅ Headers count: ${headers.length}`);
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error accessing Google Sheets: ${error.toString()}`);
  }

  return test;
}

/**
 * Test Google Forms connection
 */
function testGoogleFormsConnection() {
  const test = {
    name: 'Google Forms Connection',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const formId = PropertiesService.getScriptProperties().getProperty('GOOGLE_FORM_ID');
    const form = FormApp.openById(formId);
    
    test.details.push(`✅ Form accessible: ${form.getTitle()}`);
    test.details.push(`✅ Form items count: ${form.getItems().length}`);
    
    // Check if form is linked to sheet
    const destinationId = form.getDestinationId();
    if (destinationId) {
      test.details.push(`✅ Form linked to sheet: ${destinationId}`);
    } else {
      test.status = 'WARNING';
      test.errors.push('Form is not linked to a spreadsheet');
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error accessing Google Form: ${error.toString()}`);
  }

  return test;
}

/**
 * Test Notification Log Sheet
 */
function testNotificationLogSheet() {
  const test = {
    name: 'Notification Log Sheet',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const logSheetId = PropertiesService.getScriptProperties().getProperty('NOTIFICATIONS_LOG_SHEET_ID');
    const logSpreadsheet = SpreadsheetApp.openById(logSheetId);
    const logSheet = logSpreadsheet.getSheets()[0]; // First sheet

    test.details.push(`✅ Notification log accessible: ${logSpreadsheet.getName()}`);
    test.details.push(`✅ Log entries count: ${logSheet.getLastRow()}`);

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error accessing notification log: ${error.toString()}`);
  }

  return test;
}

/**
 * Test Prompt Document Access
 */
function testPromptDocumentAccess() {
  const test = {
    name: 'Prompt Document Access',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const docId = PropertiesService.getScriptProperties().getProperty('PROMPT_DOC_ID');
    const doc = DocumentApp.openById(docId);
    const content = doc.getBody().getText();

    test.details.push(`✅ Prompt document accessible: ${doc.getName()}`);
    test.details.push(`✅ Content length: ${content.length} characters`);

    if (content.length < 100) {
      test.status = 'WARNING';
      test.errors.push('Prompt document seems too short');
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error accessing prompt document: ${error.toString()}`);
  }

  return test;
}

/**
 * Test AI API Keys
 */
function testAIAPIKeys() {
  const test = {
    name: 'AI API Keys Configuration',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const properties = PropertiesService.getScriptProperties();
    const apiKeys = [
      'OPENAI_API_KEY',
      'MISTRAL_API_KEY',
      'GROQ_API_KEY',
      'GEMINI_API_KEY_1',
      'HUGGINGFACE_API_KEY'
    ];

    let validKeys = 0;
    apiKeys.forEach(keyName => {
      const key = properties.getProperty(keyName);
      if (key && key.length > 10) {
        validKeys++;
        test.details.push(`✅ ${keyName}: Valid format`);
      } else {
        test.errors.push(`❌ ${keyName}: Missing or invalid`);
      }
    });

    if (validKeys === 0) {
      test.status = 'FAIL';
      test.errors.push('No valid API keys found');
    } else if (validKeys < apiKeys.length) {
      test.status = 'WARNING';
      test.errors.push(`Only ${validKeys}/${apiKeys.length} API keys are valid`);
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error checking API keys: ${error.toString()}`);
  }

  return test;
}

/**
 * Test Service Account configuration
 */
function testServiceAccount() {
  const test = {
    name: 'Service Account Configuration',
    status: 'PASS',
    details: [],
    errors: []
  };

  try {
    const serviceAccountKey = PropertiesService.getScriptProperties().getProperty('SERVICE_ACCOUNT_KEY');
    
    if (!serviceAccountKey) {
      test.status = 'FAIL';
      test.errors.push('Service account key not found');
      return test;
    }

    const serviceAccount = JSON.parse(serviceAccountKey);
    const requiredFields = ['type', 'project_id', 'private_key', 'client_email'];
    
    requiredFields.forEach(field => {
      if (serviceAccount[field]) {
        test.details.push(`✅ ${field}: Present`);
      } else {
        test.status = 'FAIL';
        test.errors.push(`Missing field in service account: ${field}`);
      }
    });

    if (serviceAccount.type !== 'service_account') {
      test.status = 'FAIL';
      test.errors.push('Invalid service account type');
    }

  } catch (error) {
    test.status = 'FAIL';
    test.errors.push(`Error parsing service account: ${error.toString()}`);
  }

  return test;
}

// ===== UTILITY FUNCTIONS =====

/**
 * Generate and display test report
 */
function generateTestReport(results) {
  console.log('\n📊 TEST RESULTS SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Tests: ${results.summary.total}`);
  console.log(`✅ Passed: ${results.summary.passed}`);
  console.log(`❌ Failed: ${results.summary.failed}`);
  console.log(`⚠️ Warnings: ${results.summary.warnings}`);
  console.log(`🕐 Timestamp: ${results.timestamp}`);

  console.log('\n📋 DETAILED RESULTS');
  console.log('=' .repeat(50));

  results.tests.forEach((test, index) => {
    const statusIcon = test.status === 'PASS' ? '✅' : test.status === 'FAIL' ? '❌' : '⚠️';
    console.log(`\n${index + 1}. ${statusIcon} ${test.name}`);
    
    if (test.details.length > 0) {
      test.details.forEach(detail => console.log(`   ${detail}`));
    }
    
    if (test.errors.length > 0) {
      test.errors.forEach(error => console.log(`   ❌ ${error}`));
    }
  });

  // Overall status
  const overallStatus = results.summary.failed === 0 ? 
    (results.summary.warnings === 0 ? 'READY FOR PRODUCTION' : 'READY WITH WARNINGS') : 
    'NOT READY - FIXES REQUIRED';

  console.log('\n🎯 OVERALL STATUS');
  console.log('=' .repeat(50));
  console.log(overallStatus);

  return results;
}

/**
 * Quick health check function
 */
function quickHealthCheck() {
  console.log('🏥 Quick Health Check...');
  
  try {
    // Test basic connectivity
    const sheetId = PropertiesService.getScriptProperties().getProperty('SHEET_ID');
    const sheet = SpreadsheetApp.openById(sheetId).getSheetByName('Aqar_bot');
    const rowCount = sheet.getLastRow();
    
    console.log(`✅ System is responsive`);
    console.log(`✅ Main sheet accessible with ${rowCount} rows`);
    console.log(`✅ Last check: ${new Date()}`);
    
    return true;
  } catch (error) {
    console.log(`❌ Health check failed: ${error.toString()}`);
    return false;
  }
}
