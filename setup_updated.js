/**
 * Setup script to initialize the Aqar Bot project
 * Run this once to set up all script properties
 * 
 * Project: Real Estate Data Processing System
 * Version: 2.0
 * Last Updated: 2025-07-30
 */

function setupProject() {
  console.log('Starting Aqar Bot setup...');
  
  const scriptProperties = PropertiesService.getScriptProperties();

  // Project Configuration
  const properties = {
    // ===== Google Forms & Sheets Configuration =====
    'GOOGLE_FORM_ID': '1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw',
    'GOOGLE_FORM_URL': 'https://docs.google.com/forms/d/e/1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw/viewform',
    'SHEET_ID': '1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c',
    'SHEET_NAME': 'Aqar_bot',
    'NOTIFICATIONS_LOG_SHEET_ID': '1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM',
    'PROMPT_DOC_ID': '14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk',

    // ===== Project Names (Arabic) =====
    'FORM_NAME': 'ضع بيان عقارك هنا',
    'FOLDER_NAME': 'Elsweedy real estate data',
    'MAIN_SHEET_NAME': 'Elsweedy real estate sheet',
    'NOTIFICATION_SHEET_NAME': 'notification log',
    'PROMPT_DOC_NAME': 'Flexible Real Estate Analysis Prompt',

    // ===== Google Chat Configuration =====
    // Note: These need to be configured when setting up Google Chat Bot
    'CHAT_SPACE_ID': '', // To be configured later
    'CHAT_APP_ID': '', // To be configured later
    'CHAT_WEBHOOK_URL': '', // To be configured later

    // ===== AI API Keys =====
    // WARNING: These should be moved to environment variables in production
    'OPENAI_API_KEY': '********************************************************************************************************************************************************************',
    'MISTRAL_API_KEY': 'soMr4s2jGPzGrKO00BOjOh7Vrhb5IxMP',
    'GROQ_API_KEY': '********************************************************',
    'HUGGINGFACE_API_KEY': '*************************************',
    
    // Multiple Gemini API Keys for redundancy
    'GEMINI_API_KEY_1': 'AIzaSyC6hgtBF5ILBI8WGOmjavjEO3b699cL8A8',
    'GEMINI_API_KEY_2': 'AIzaSyDIEBBrQLsdJO3tIELc0zFuJVk1efZOo9c',
    'GEMINI_API_KEY_3': 'AIzaSyDboufcqfd5iVhmO3eRogqA4e3FUJz_fw8',

    // ===== AI Models Configuration =====
    'OPENAI_MODEL': 'gpt-4',
    'MISTRAL_MODEL': 'mistral-large',
    'GROQ_MODEL': 'llama3-70b-8192',
    'HUGGINGFACE_MODEL': 'meta-llama/Llama-2-70b-chat-hf',
    'GEMINI_MODEL': 'gemini-pro',

    // ===== Service Account Configuration =====
    'SERVICE_ACCOUNT_KEY': JSON.stringify(********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************),

    // ===== Field Names (Arabic) =====
    'UNIT_CODE_COL': 'كود الوحدة',
    'STATUS_COL': 'Status',
    'SUCCESS_STATES_COL': 'حالات النجاح',
    'PROPERTY_TYPE_QUESTION': 'نوع البيان اللي بتسجله     ⁉️صله',
    'SINGLE_VALUE_FIELD': '🪀  بيان  عقار  غير مفصل',
    'DETAILED_TYPE': 'بيان  مفصل  💘',
    'MULTI_VALUE_FIELD': '🔥  بيانات  متعددة  🔥',

    // ===== Required Fields =====
    'REQUIRED_FIELDS': JSON.stringify(['المنطقة', 'نوع الوحدة', 'حالة الوحدة']),

    // ===== Default Values =====
    'DEFAULT_TEXT_VALUE': 'غير محدد',
    'DEFAULT_NUMBER_VALUE': '0',
    'DEFAULT_PHONE_VALUE': '***********'
  };

  try {
    // Set all properties
    scriptProperties.setProperties(properties);
    console.log('✅ All properties set successfully!');
    
    // Verify setup
    verifySetup();
    
    return {
      success: true,
      message: 'Aqar Bot setup completed successfully',
      propertiesCount: Object.keys(properties).length
    };
    
  } catch (error) {
    console.error('❌ Error setting up properties:', error);
    return {
      success: false,
      error: error.toString()
    };
  }
}

/**
 * Verify that all required properties are set
 */
function verifySetup() {
  const scriptProperties = PropertiesService.getScriptProperties();
  const requiredProps = [
    'GOOGLE_FORM_ID',
    'SHEET_ID',
    'NOTIFICATIONS_LOG_SHEET_ID',
    'PROMPT_DOC_ID',
    'OPENAI_API_KEY',
    'SERVICE_ACCOUNT_KEY'
  ];

  console.log('🔍 Verifying setup...');
  
  for (const prop of requiredProps) {
    const value = scriptProperties.getProperty(prop);
    if (!value) {
      console.error(`❌ Missing property: ${prop}`);
    } else {
      console.log(`✅ ${prop}: Set`);
    }
  }
}

/**
 * Clear all script properties (use with caution)
 */
function clearAllProperties() {
  const scriptProperties = PropertiesService.getScriptProperties();
  scriptProperties.deleteAllProperties();
  console.log('🗑️ All properties cleared');
}

/**
 * List all current properties
 */
function listAllProperties() {
  const scriptProperties = PropertiesService.getScriptProperties();
  const properties = scriptProperties.getProperties();
  
  console.log('📋 Current Properties:');
  Object.keys(properties).forEach(key => {
    // Hide sensitive data
    if (key.includes('API_KEY') || key.includes('SERVICE_ACCOUNT')) {
      console.log(`${key}: [HIDDEN]`);
    } else {
      console.log(`${key}: ${properties[key]}`);
    }
  });
  
  return properties;
}

// Export functions for testing
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    setupProject,
    verifySetup,
    clearAllProperties,
    listAllProperties
  };
}
