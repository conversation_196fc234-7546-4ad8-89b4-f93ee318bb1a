{"name": "aqar-bot", "version": "2.0.0", "description": "نظام ذكي لمعالجة وتحليل البيانات العقارية باستخدام الذكاء الاصطناعي ومنصة Google Apps Script", "main": "main.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "deploy": "clasp push", "setup": "clasp create --type standalone", "login": "clasp login", "logs": "clasp logs", "open": "clasp open"}, "keywords": ["real-estate", "ai", "google-apps-script", "automation", "data-processing", "arabic", "property-management", "google-sheets", "google-forms", "chatbot"], "author": {"name": "Elsweedy Real Estate Team", "email": "<EMAIL>"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/elsweedy-realestate/aqar-bot.git"}, "bugs": {"url": "https://github.com/elsweedy-realestate/aqar-bot/issues"}, "homepage": "https://github.com/elsweedy-realestate/aqar-bot#readme", "engines": {"node": ">=14.0.0"}, "devDependencies": {"@google/clasp": "^2.4.2", "@types/google-apps-script": "^1.0.83"}, "config": {"google_apps_script": {"project_id": "aqar-bot-467120", "script_id": "YOUR_SCRIPT_ID_HERE"}}, "aqar_bot": {"version": "2.0.0", "last_updated": "2025-07-30", "components": {"main": "main.js", "utils": "utils.js", "validation": "validation.js", "notifications": "notifications.js", "setup": "setup_updated.js", "tests": "test_connections.js"}, "google_services": {"forms": {"id": "1FAIpQLSfcVgW2pUOz9_NNBcoo4pT_KQmNM5bnVPTL0CmN_VM-6cAKsw", "name": "ضع بيان عقارك هنا"}, "sheets": {"main": {"id": "1Qwfv9m4mUJ6NB0aFrTqgTxUKdkJggq67zm63T4HqR1c", "name": "Elsweedy real estate sheet", "worksheet": "Aqar_bot"}, "notifications": {"id": "1L7cD6H1kW6xR7B0g8WSl-7rs-AM6c5AEq_UJpEFzhKM", "name": "notification log"}}, "docs": {"prompt": {"id": "14dYTyvqn1V38OtH7TJy1GDoww34NgRUDbVZCySWWpvk", "name": "Flexible Real Estate Analysis Prompt"}}}, "ai_platforms": ["OpenAI GPT-4", "Mistral Large", "Groq Llama3-70B", "Google Gemini Pro", "HuggingFace Llama2-70B"], "features": ["Form data processing", "AI-powered analysis", "Multi-platform AI support", "Real-time notifications", "Duplicate detection", "Data validation", "Fallback processing", "Comprehensive logging"], "languages": ["Arabic", "English"], "deployment": {"platform": "Google Apps Script", "environment": "Cloud", "triggers": ["Form submission", "Time-based (AI processing)", "Time-based (Fallback processing)"]}}}